<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\AetherProject\Aether Client\app\src\main\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main" generated-set="main$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\AetherProject\Aether Client\app\src\main\res"><file name="ic_launcher" path="D:\AetherProject\Aether Client\app\src\main\res\drawable\ic_launcher.png" qualifiers="" type="drawable"/><file name="ic_launcher_background" path="D:\AetherProject\Aether Client\app\src\main\res\drawable\ic_launcher_background.xml" qualifiers="" type="drawable"/><file name="ic_launcher" path="D:\AetherProject\Aether Client\app\src\main\res\mipmap-anydpi-v26\ic_launcher.xml" qualifiers="anydpi-v26" type="mipmap"/><file name="ic_launcher_round" path="D:\AetherProject\Aether Client\app\src\main\res\mipmap-anydpi-v26\ic_launcher_round.xml" qualifiers="anydpi-v26" type="mipmap"/><file name="ic_launcher" path="D:\AetherProject\Aether Client\app\src\main\res\mipmap-hdpi\ic_launcher.webp" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher_foreground" path="D:\AetherProject\Aether Client\app\src\main\res\mipmap-hdpi\ic_launcher_foreground.webp" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="D:\AetherProject\Aether Client\app\src\main\res\mipmap-hdpi\ic_launcher_round.webp" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher" path="D:\AetherProject\Aether Client\app\src\main\res\mipmap-mdpi\ic_launcher.webp" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher_foreground" path="D:\AetherProject\Aether Client\app\src\main\res\mipmap-mdpi\ic_launcher_foreground.webp" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="D:\AetherProject\Aether Client\app\src\main\res\mipmap-mdpi\ic_launcher_round.webp" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher" path="D:\AetherProject\Aether Client\app\src\main\res\mipmap-xhdpi\ic_launcher.webp" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher_foreground" path="D:\AetherProject\Aether Client\app\src\main\res\mipmap-xhdpi\ic_launcher_foreground.webp" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="D:\AetherProject\Aether Client\app\src\main\res\mipmap-xhdpi\ic_launcher_round.webp" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher" path="D:\AetherProject\Aether Client\app\src\main\res\mipmap-xxhdpi\ic_launcher.webp" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher_foreground" path="D:\AetherProject\Aether Client\app\src\main\res\mipmap-xxhdpi\ic_launcher_foreground.webp" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="D:\AetherProject\Aether Client\app\src\main\res\mipmap-xxhdpi\ic_launcher_round.webp" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher" path="D:\AetherProject\Aether Client\app\src\main\res\mipmap-xxxhdpi\ic_launcher.webp" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="ic_launcher_foreground" path="D:\AetherProject\Aether Client\app\src\main\res\mipmap-xxxhdpi\ic_launcher_foreground.webp" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="D:\AetherProject\Aether Client\app\src\main\res\mipmap-xxxhdpi\ic_launcher_round.webp" qualifiers="xxxhdpi-v4" type="mipmap"/><file path="D:\AetherProject\Aether Client\app\src\main\res\values\colors.xml" qualifiers=""><color name="app_icon_background_color">#5865F2</color><color name="aether_primary">#3B82F6</color><color name="aether_primary_variant">#6366F1</color><color name="aether_secondary">#10B981</color><color name="aether_secondary_variant">#06B6D4</color><color name="aether_accent">#EF4444</color><color name="aether_accent_variant">#F59E0B</color><color name="aether_background">#0F172A</color><color name="aether_background_variant">#1E293B</color><color name="aether_surface">#334155</color><color name="aether_surface_variant">#475569</color><color name="aether_on_primary">#FFFFFF</color><color name="aether_on_secondary">#FFFFFF</color><color name="aether_on_background">#F8FAFC</color><color name="aether_on_surface">#E2E8F0</color><color name="aether_on_surface_variant">#CBD5E1</color><color name="aether_neon_green">#00FF9F</color><color name="aether_neon_purple">#9945FF</color><color name="aether_neon_blue">#14F0FF</color><color name="aether_neon_pink">#FF0A78</color><color name="aether_success">#10B981</color><color name="aether_warning">#F59E0B</color><color name="aether_error">#EF4444</color><color name="aether_info">#3B82F6</color></file><file path="D:\AetherProject\Aether Client\app\src\main\res\values\strings.xml" qualifiers=""><string name="app_name" translatable="false">Aether</string><string name="third_party_licenses_title">Third-Party Licenses</string><string name="third_party_licenses_description">AetherClient uses several open source libraries. We acknowledge and thank all contributors to these projects.</string><string name="home">Home</string><string name="what_is_this">What\'s this?</string><string name="introduction">AetherClient is a Minecraft Bedrock client that utilizes a MITM approach, providing various utilities to enhance the gameplay experience without modifying the game\'s memory.</string><string name="backend" translatable="false">AetherRelay</string><string name="backend_introduction">Set up a MITM-based interception in a few simple steps.</string><string name="minecraft" translatable="false">Minecraft</string><string name="recommended_version">Recommended version: %s</string><string name="capturing_game_packets">Capturing game packets...</string><string name="stop">Stop</string><string name="overlay_permission_denied">Overlay permission denied</string><string name="notification_permission_denied">Notification permission denied</string><string name="request_overlay_permission">We need overlay permission to display the function floating window</string><string name="game_settings">Game Settings</string><string name="confirm">Confirm</string><string name="cancel">Cancel</string><string name="server_host_name">Server host name</string><string name="server_port">Server port</string><string name="tips">Tips</string><string name="change_game_settings_tip">You need to disconnect before changing game settings.</string><string name="backend_connected">AetherRelay connected.</string><string name="start_game">Start Game</string><string name="backend_disconnected">AetherRelay disconnected.</string><string name="select_game">Select game</string><string name="no_game_selected">No game selected</string><string name="game_selector">Game Selector</string><string name="select_game_first">Please select a game first.</string><string name="failed_to_launch_game">Failed to launch the game.</string><string name="account">Account</string><string name="add_account">Add Account</string><string name="fetch_account_failed">Fetch account failed: %s</string><string name="delete">Delete</string><string name="select">Select</string><string name="unselect">Unselect</string><string name="has_been_selected">(Selected)</string><string name="server">Servers</string><string name="about">About</string><string name="settings">Settings</string><string name="how_do_i_switch_login_mode">How do I switch login modes?</string><string name="login_mode_introduction">If you want to log in to online mode, add an account and select it. If you want to log in to offline mode, deselect the account.</string><string name="combat">Combat</string><string name="motion">Motion</string><string name="visual">Visual</string><string name="particle">Particle</string><string name="misc">Misc</string><string name="config">Config</string><string name="overlay_opacity">Overlay Button Opacity</string><string name="shortcut_opacity">Shortcut Button Opacity</string><string name="overlay_icon">Overlay Icon</string><string name="overlay_icon_description">Change the floating button icon</string><string name="overlay_opacity_settings">Opacity Settings</string><string name="overlay_opacity_description">Adjust transparency of overlay and shortcut buttons</string><string name="overlay_border_color">Overlay Border Color</string><string name="overlay_border_color_description">Change the border color of the floating button</string><string name="request_ignore_battery_optimization">We need to ignore battery optimization to stay connected</string><string name="ignore_battery_optimization_denied">Ignore battery optimization permission denied</string><string name="shortcut">Shortcut</string><string name="check_updates_and_news">View Updates &amp; News</string><string name="crash_happened">Crash Happened</string><string name="cannot_back">A crash has occurred and you are not allowed to return. You can only restart the app.</string><string name="effect">Effect</string><string name="login_in">Log in using %s</string><string name="fetch_account_successfully">Fetch account successfully</string><string name="xbox_device_code">Xbox device code</string><string name="refresh">Refresh</string></file><file path="D:\AetherProject\Aether Client\app\src\main\res\values\themes.xml" qualifiers=""><style name="Theme.AetherClient" parent="android:Theme.Material.Light.NoActionBar"/></file><file name="file_paths" path="D:\AetherProject\Aether Client\app\src\main\res\xml\file_paths.xml" qualifiers="" type="xml"/><file name="network_security_config" path="D:\AetherProject\Aether Client\app\src\main\res\xml\network_security_config.xml" qualifiers="" type="xml"/></source></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="release$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\AetherProject\Aether Client\app\src\release\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="release" generated-set="release$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\AetherProject\Aether Client\app\src\release\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"/><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated" generated-set="generated$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"/><mergedItems/></merger>