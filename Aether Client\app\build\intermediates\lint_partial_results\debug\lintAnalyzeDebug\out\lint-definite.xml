<?xml version="1.0" encoding="UTF-8"?>
<incidents format="6" by="lint 8.11.1" type="incidents">

    <incident
        id="DefaultUncaughtExceptionDelegation"
        severity="warning"
        message="Must call `getDefaultUncaughtExceptionHandler()` to get the existing handler, and call `existingHandler.uncaughtException(thread, throwable)` from your new handler">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/radiantbyte/aetherclient/app/AetherApp.kt"
            line="49"
            column="9"
            startOffset="1369"
            endLine="49"
            endColumn="64"
            endOffset="1424"/>
    </incident>

    <incident
        id="AndroidGradlePluginVersion"
        severity="warning"
        message="A newer version of Gradle than 8.13 is available: 8.14.3">
        <fix-replace
            description="Update to 8.14.3"
            oldString="8.13"
            replacement="8.14.3"
            priority="0"/>
        <location
            file="../gradle/wrapper/gradle-wrapper.properties"
            line="3"
            column="17"
            startOffset="81"
            endLine="3"
            endColumn="79"
            endOffset="143"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of androidx.lifecycle:lifecycle-runtime-ktx than 2.9.1 is available: 2.9.2">
        <fix-replace
            description="Change to 2.9.2"
            family="Update versions"
            oldString="2.9.1"
            replacement="2.9.2"
            priority="0"/>
        <location
            file="../gradle/libs.versions.toml"
            line="10"
            column="23"
            startOffset="221"
            endLine="10"
            endColumn="30"
            endOffset="228"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of androidx.compose:compose-bom than 2025.06.01 is available: 2025.07.00">
        <fix-replace
            description="Change to 2025.07.00"
            family="Update versions"
            oldString="2025.06.01"
            replacement="2025.07.00"
            priority="0"/>
        <location
            file="../gradle/libs.versions.toml"
            line="12"
            column="14"
            startOffset="269"
            endLine="12"
            endColumn="26"
            endOffset="281"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of androidx.navigation:navigation-compose than 2.9.1 is available: 2.9.2">
        <fix-replace
            description="Change to 2.9.2"
            family="Update versions"
            oldString="2.9.1"
            replacement="2.9.2"
            priority="0"/>
        <location
            file="../gradle/libs.versions.toml"
            line="14"
            column="21"
            startOffset="334"
            endLine="14"
            endColumn="28"
            endOffset="341"/>
    </incident>

    <incident
        id="NewerVersionAvailable"
        severity="warning"
        message="A newer version of org.apache.logging.log4j:log4j-bom than 2.25.0 is available: 2.25.1">
        <fix-replace
            description="Change to 2.25.1"
            family="Update versions"
            oldString="2.25.0"
            replacement="2.25.1"
            priority="0"/>
        <location
            file="../gradle/libs.versions.toml"
            line="16"
            column="9"
            startOffset="351"
            endLine="16"
            endColumn="17"
            endOffset="359"/>
    </incident>

    <incident
        id="NewerVersionAvailable"
        severity="warning"
        message="A newer version of com.fasterxml.jackson.core:jackson-databind than 2.19.1 is available: 2.19.2">
        <fix-replace
            description="Change to 2.19.2"
            family="Update versions"
            oldString="2.19.1"
            replacement="2.19.2"
            priority="0"/>
        <location
            file="../gradle/libs.versions.toml"
            line="27"
            column="20"
            startOffset="580"
            endLine="27"
            endColumn="28"
            endOffset="588"/>
    </incident>

    <incident
        id="DiscouragedApi"
        severity="warning"
        message="Fixed screen orientations will be ignored in most cases, starting from Android 16. Android is moving toward a model where apps are expected to adapt to various orientations, display sizes, and aspect ratios.">
        <show-url
            description="Show https://developer.android.com/adaptive-apps"
            url="https://developer.android.com/adaptive-apps"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*manifest*0}"
            line="39"
            column="13"
            startOffset="1723"
            endLine="39"
            endColumn="50"
            endOffset="1760"/>
    </incident>

    <incident
        id="WakelockTimeout"
        severity="warning"
        message="Provide a timeout when requesting a wakelock with `PowerManager.Wakelock.acquire(long timeout)`. This will ensure the OS will cleanup any wakelocks that last longer than you intend, and will save your user&apos;s battery.">
        <fix-replace
            description="Set timeout to 10 minutes"
            oldPattern="acquire\s*\(()\s*\)"
            replacement="10*60*1000L /*10 minutes*/"
            priority="0"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/radiantbyte/aetherclient/service/AetherEngine.kt"
            line="68"
            column="17"
            startOffset="2803"
            endLine="68"
            endColumn="26"
            endOffset="2812"/>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; `SDK_INT` is always >= 28">
        <fix-data conditional="true"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/radiantbyte/aetherclient/service/AetherEngine.kt"
            line="327"
            column="28"
            startOffset="11669"
            endLine="327"
            endColumn="74"
            endOffset="11715"/>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; `SDK_INT` is always >= 28">
        <fix-data conditional="true"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/radiantbyte/aetherclient/util/MinecraftVersionDetector.kt"
            line="40"
            column="35"
            startOffset="1260"
            endLine="40"
            endColumn="81"
            endOffset="1306"/>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="This folder configuration (`v26`) is unnecessary; `minSdkVersion` is 28. Merge all the resources in this folder into `mipmap-anydpi`.">
        <fix-data file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/mipmap-anydpi-v26" folderName="mipmap-anydpi" requiresApi="28"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/mipmap-anydpi-v26"/>
    </incident>

    <incident
        id="TypographyEllipsis"
        severity="warning"
        message="Replace &quot;...&quot; with ellipsis character (…, &amp;#8230;) ?">
        <fix-replace
            description="Replace with …"
            oldString="..."
            replacement="…"
            priority="0"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="14"
            column="43"
            startOffset="1036"
            endLine="14"
            endColumn="68"
            endOffset="1061"/>
    </incident>

    <incident
        id="IconLauncherShape"
        severity="warning"
        message="Launcher icons should not fill every pixel of their square region; see the design guide for details">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_launcher.png"/>
    </incident>

    <incident
        id="MonochromeLauncherIcon"
        severity="warning"
        message="The application adaptive icon is missing a monochrome tag">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/mipmap-anydpi-v26/ic_launcher.xml"
            line="2"
            column="1"
            startOffset="40"
            endLine="5"
            endColumn="17"
            endOffset="272"/>
    </incident>

    <incident
        id="MonochromeLauncherIcon"
        severity="warning"
        message="The application adaptive roundIcon is missing a monochrome tag">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/mipmap-anydpi-v26/ic_launcher_round.xml"
            line="2"
            column="1"
            startOffset="40"
            endLine="5"
            endColumn="17"
            endOffset="272"/>
    </incident>

    <incident
        id="IconLocation"
        severity="warning"
        message="Found bitmap drawable `res/drawable/ic_launcher.png` in densityless folder">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_launcher.png"/>
    </incident>

</incidents>
