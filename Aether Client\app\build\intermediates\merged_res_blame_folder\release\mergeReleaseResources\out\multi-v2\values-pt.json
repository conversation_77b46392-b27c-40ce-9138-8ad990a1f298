{"logs": [{"outputFile": "com.radiantbyte.aetherclient.app-mergeReleaseResources-46:/values-pt/values-pt.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\76f6aeeb7c72cabab8c29d18f4d3161d\\transformed\\core-1.16.0\\res\\values-pt\\values-pt.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,353,453,563,673,793", "endColumns": "96,101,98,99,109,109,119,100", "endOffsets": "147,249,348,448,558,668,788,889"}, "to": {"startLines": "3,4,5,6,7,8,9,83", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "196,293,395,494,594,704,814,8577", "endColumns": "96,101,98,99,109,109,119,100", "endOffsets": "288,390,489,589,699,809,929,8673"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\********************************\\transformed\\material3-release\\res\\values-pt\\values-pt.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,174,295,411,527,629,726,840,974,1092,1244,1328,1429,1524,1624,1739,1869,1975,2114,2250,2381,2547,2674,2794,2918,3038,3134,3231,3351,3467,3567,3678,3787,3927,4072,4182,4285,4371,4465,4557,4673,4763,4852,4953,5033,5117,5218,5324,5416,5515,5603,5715,5816,5920,6039,6119,6219", "endColumns": "118,120,115,115,101,96,113,133,117,151,83,100,94,99,114,129,105,138,135,130,165,126,119,123,119,95,96,119,115,99,110,108,139,144,109,102,85,93,91,115,89,88,100,79,83,100,105,91,98,87,111,100,103,118,79,99,91", "endOffsets": "169,290,406,522,624,721,835,969,1087,1239,1323,1424,1519,1619,1734,1864,1970,2109,2245,2376,2542,2669,2789,2913,3033,3129,3226,3346,3462,3562,3673,3782,3922,4067,4177,4280,4366,4460,4552,4668,4758,4847,4948,5028,5112,5213,5319,5411,5510,5598,5710,5811,5915,6034,6114,6214,6306"}, "to": {"startLines": "17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1577,1696,1817,1933,2049,2151,2248,2362,2496,2614,2766,2850,2951,3046,3146,3261,3391,3497,3636,3772,3903,4069,4196,4316,4440,4560,4656,4753,4873,4989,5089,5200,5309,5449,5594,5704,5807,5893,5987,6079,6195,6285,6374,6475,6555,6639,6740,6846,6938,7037,7125,7237,7338,7442,7561,7641,7741", "endColumns": "118,120,115,115,101,96,113,133,117,151,83,100,94,99,114,129,105,138,135,130,165,126,119,123,119,95,96,119,115,99,110,108,139,144,109,102,85,93,91,115,89,88,100,79,83,100,105,91,98,87,111,100,103,118,79,99,91", "endOffsets": "1691,1812,1928,2044,2146,2243,2357,2491,2609,2761,2845,2946,3041,3141,3256,3386,3492,3631,3767,3898,4064,4191,4311,4435,4555,4651,4748,4868,4984,5084,5195,5304,5444,5589,5699,5802,5888,5982,6074,6190,6280,6369,6470,6550,6634,6735,6841,6933,7032,7120,7232,7333,7437,7556,7636,7736,7828"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\d61819a9c261b5407a6ee59c046430b9\\transformed\\ui-release\\res\\values-pt\\values-pt.xml", "from": {"startLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "197,292,378,475,574,660,743,840,931,1018,1103,1193,1269,1354,1430,1509,1584,1660,1727", "endColumns": "94,85,96,98,85,82,96,90,86,84,89,75,84,75,78,74,75,66,112", "endOffsets": "287,373,470,569,655,738,835,926,1013,1098,1188,1264,1349,1425,1504,1579,1655,1722,1835"}, "to": {"startLines": "10,11,12,13,14,15,16,74,75,76,77,78,79,80,81,82,84,85,86", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "934,1029,1115,1212,1311,1397,1480,7833,7924,8011,8096,8186,8262,8347,8423,8502,8678,8754,8821", "endColumns": "94,85,96,98,85,82,96,90,86,84,89,75,84,75,78,74,75,66,112", "endOffsets": "1024,1110,1207,1306,1392,1475,1572,7919,8006,8091,8181,8257,8342,8418,8497,8572,8749,8816,8929"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\7abcdc53ecef1f011b8484f7921702b8\\transformed\\foundation-release\\res\\values-pt\\values-pt.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,146,229", "endColumns": "90,82,84", "endOffsets": "141,224,309"}, "to": {"startLines": "2,87,88", "startColumns": "4,4,4", "startOffsets": "105,8934,9017", "endColumns": "90,82,84", "endOffsets": "191,9012,9097"}}]}]}