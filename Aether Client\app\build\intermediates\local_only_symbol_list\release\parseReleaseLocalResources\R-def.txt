R_DEF: Internal format may change without notice
local
color aether_accent
color aether_accent_variant
color aether_background
color aether_background_variant
color aether_error
color aether_info
color aether_neon_blue
color aether_neon_green
color aether_neon_pink
color aether_neon_purple
color aether_on_background
color aether_on_primary
color aether_on_secondary
color aether_on_surface
color aether_on_surface_variant
color aether_primary
color aether_primary_variant
color aether_secondary
color aether_secondary_variant
color aether_success
color aether_surface
color aether_surface_variant
color aether_warning
color app_icon_background_color
drawable ic_launcher
drawable ic_launcher_background
mipmap ic_launcher
mipmap ic_launcher_foreground
mipmap ic_launcher_round
string about
string account
string add_account
string app_name
string backend
string backend_connected
string backend_disconnected
string backend_introduction
string cancel
string cannot_back
string capturing_game_packets
string change_game_settings_tip
string check_updates_and_news
string combat
string config
string confirm
string crash_happened
string delete
string effect
string failed_to_launch_game
string fetch_account_failed
string fetch_account_successfully
string game_selector
string game_settings
string has_been_selected
string home
string how_do_i_switch_login_mode
string ignore_battery_optimization_denied
string introduction
string login_in
string login_mode_introduction
string minecraft
string misc
string motion
string no_game_selected
string notification_permission_denied
string overlay_border_color
string overlay_border_color_description
string overlay_icon
string overlay_icon_description
string overlay_opacity
string overlay_opacity_description
string overlay_opacity_settings
string overlay_permission_denied
string particle
string recommended_version
string refresh
string request_ignore_battery_optimization
string request_overlay_permission
string select
string select_game
string select_game_first
string server
string server_host_name
string server_port
string settings
string shortcut
string shortcut_opacity
string start_game
string stop
string third_party_licenses_description
string third_party_licenses_title
string tips
string unselect
string visual
string what_is_this
string xbox_device_code
style Theme.AetherClient
xml file_paths
xml network_security_config
