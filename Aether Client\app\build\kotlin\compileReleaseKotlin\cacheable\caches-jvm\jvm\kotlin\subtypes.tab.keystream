#androidx.activity.ComponentActivityandroid.app.Application)java.lang.Thread.UncaughtExceptionHandlerkotlin.Enum5com.radiantbyte.aetherclient.model.RealmsLoadingState8com.radiantbyte.aetherclient.overlay.AetherOverlayWindow+androidx.savedstate.SavedStateRegistryOwnerandroid.view.View!androidx.lifecycle.LifecycleOwnerandroid.app.Service:com.radiantbyte.aetherclient.service.AetherProxyConnectionandroid.webkit.WebViewandroid.webkit.WebViewClientandroidx.lifecycle.ViewModel                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                               