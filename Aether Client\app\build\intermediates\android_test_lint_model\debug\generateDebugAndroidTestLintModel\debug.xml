<variant
    name="debug"
    package="com.radiantbyte.aetherclient"
    minSdkVersion="28"
    targetSdkVersion="36.0"
    debuggable="true"
    mergedManifest="build\intermediates\merged_manifest\debug\processDebugMainManifest\AndroidManifest.xml"
    manifestMergeReport="build\outputs\logs\manifest-merger-debug-report.txt"
    partialResultsDir="build\intermediates\android_test_lint_partial_results\debug\lintAnalyzeDebugAndroidTest\out">
  <buildFeatures
      namespacing="REQUIRED"/>
  <sourceProviders>
  </sourceProviders>
  <testSourceProviders>
    <sourceProvider
        manifests="src\androidTest\AndroidManifest.xml"
        javaDirectories="src\androidTest\java;src\androidTestDebug\java;src\androidTest\kotlin;src\androidTestDebug\kotlin"
        resDirectories="src\androidTest\res;src\androidTestDebug\res"
        assetsDirectories="src\androidTest\assets;src\androidTestDebug\assets"
        androidTest="true"/>
  </testSourceProviders>
  <testFixturesSourceProviders>
  </testFixturesSourceProviders>
  <artifact
      type="INSTRUMENTATION_TEST"
      applicationId="com.radiantbyte.aetherclient.test"
      desugaredMethodsFiles="C:\Users\<USER>\.gradle\caches\8.13\transforms\5aa2c574ea3f161eff9fa2f10c9fd80d\transformed\D8BackportedDesugaredMethods.txt">
  </artifact>
</variant>
