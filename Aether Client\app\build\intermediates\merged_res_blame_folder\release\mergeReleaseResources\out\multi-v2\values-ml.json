{"logs": [{"outputFile": "com.radiantbyte.aetherclient.app-mergeReleaseResources-46:/values-ml/values-ml.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\76f6aeeb7c72cabab8c29d18f4d3161d\\transformed\\core-1.16.0\\res\\values-ml\\values-ml.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,157,260,362,466,569,670,792", "endColumns": "101,102,101,103,102,100,121,100", "endOffsets": "152,255,357,461,564,665,787,888"}, "to": {"startLines": "3,4,5,6,7,8,9,83", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "192,294,397,499,603,706,807,8823", "endColumns": "101,102,101,103,102,100,121,100", "endOffsets": "289,392,494,598,701,802,924,8919"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\d61819a9c261b5407a6ee59c046430b9\\transformed\\ui-release\\res\\values-ml\\values-ml.xml", "from": {"startLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "193,288,375,474,578,668,754,855,942,1030,1116,1203,1281,1373,1450,1524,1597,1673,1740", "endColumns": "94,86,98,103,89,85,100,86,87,85,86,77,91,76,73,72,75,66,118", "endOffsets": "283,370,469,573,663,749,850,937,1025,1111,1198,1276,1368,1445,1519,1592,1668,1735,1854"}, "to": {"startLines": "10,11,12,13,14,15,16,74,75,76,77,78,79,80,81,82,84,85,86", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "929,1024,1111,1210,1314,1404,1490,8081,8168,8256,8342,8429,8507,8599,8676,8750,8924,9000,9067", "endColumns": "94,86,98,103,89,85,100,86,87,85,86,77,91,76,73,72,75,66,118", "endOffsets": "1019,1106,1205,1309,1399,1485,1586,8163,8251,8337,8424,8502,8594,8671,8745,8818,8995,9062,9181"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\7abcdc53ecef1f011b8484f7921702b8\\transformed\\foundation-release\\res\\values-ml\\values-ml.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,142,230", "endColumns": "86,87,91", "endOffsets": "137,225,317"}, "to": {"startLines": "2,87,88", "startColumns": "4,4,4", "startOffsets": "105,9186,9274", "endColumns": "86,87,91", "endOffsets": "187,9269,9361"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\13ceaa855c6ae6b23241ae42f3bae2bf\\transformed\\material3-release\\res\\values-ml\\values-ml.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,175,301,427,550,650,744,855,1007,1125,1282,1367,1472,1572,1674,1797,1930,2040,2176,2318,2449,2653,2787,2911,3041,3175,3276,3374,3492,3623,3722,3824,3937,4075,4221,4335,4444,4520,4618,4718,4832,4919,5016,5124,5204,5292,5390,5503,5598,5709,5799,5914,6016,6129,6261,6341,6448", "endColumns": "119,125,125,122,99,93,110,151,117,156,84,104,99,101,122,132,109,135,141,130,203,133,123,129,133,100,97,117,130,98,101,112,137,145,113,108,75,97,99,113,86,96,107,79,87,97,112,94,110,89,114,101,112,131,79,106,96", "endOffsets": "170,296,422,545,645,739,850,1002,1120,1277,1362,1467,1567,1669,1792,1925,2035,2171,2313,2444,2648,2782,2906,3036,3170,3271,3369,3487,3618,3717,3819,3932,4070,4216,4330,4439,4515,4613,4713,4827,4914,5011,5119,5199,5287,5385,5498,5593,5704,5794,5909,6011,6124,6256,6336,6443,6540"}, "to": {"startLines": "17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1591,1711,1837,1963,2086,2186,2280,2391,2543,2661,2818,2903,3008,3108,3210,3333,3466,3576,3712,3854,3985,4189,4323,4447,4577,4711,4812,4910,5028,5159,5258,5360,5473,5611,5757,5871,5980,6056,6154,6254,6368,6455,6552,6660,6740,6828,6926,7039,7134,7245,7335,7450,7552,7665,7797,7877,7984", "endColumns": "119,125,125,122,99,93,110,151,117,156,84,104,99,101,122,132,109,135,141,130,203,133,123,129,133,100,97,117,130,98,101,112,137,145,113,108,75,97,99,113,86,96,107,79,87,97,112,94,110,89,114,101,112,131,79,106,96", "endOffsets": "1706,1832,1958,2081,2181,2275,2386,2538,2656,2813,2898,3003,3103,3205,3328,3461,3571,3707,3849,3980,4184,4318,4442,4572,4706,4807,4905,5023,5154,5253,5355,5468,5606,5752,5866,5975,6051,6149,6249,6363,6450,6547,6655,6735,6823,6921,7034,7129,7240,7330,7445,7547,7660,7792,7872,7979,8076"}}]}]}