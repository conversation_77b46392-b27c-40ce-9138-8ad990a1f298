{"logs": [{"outputFile": "com.radiantbyte.aetherclient.app-mergeReleaseResources-46:/values/values.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\fe3938a7b35ed01afb57a303c0783491\\transformed\\startup-runtime-1.1.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "82", "endOffsets": "133"}, "to": {"startLines": "131", "startColumns": "4", "startOffsets": "7691", "endColumns": "82", "endOffsets": "7769"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\7abcdc53ecef1f011b8484f7921702b8\\transformed\\foundation-release\\res\\values\\values.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,101,157", "endColumns": "45,55,54", "endOffsets": "96,152,207"}, "to": {"startLines": "133,281,282", "startColumns": "4,4,4", "startOffsets": "7839,18290,18346", "endColumns": "45,55,54", "endOffsets": "7880,18341,18396"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\3560ae3ebe7eddcdf5e1a10444b6136f\\transformed\\customview-poolingcontainer-1.0.0\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,109", "endColumns": "53,66", "endOffsets": "104,171"}, "to": {"startLines": "96,100", "startColumns": "4,4", "startOffsets": "5850,6027", "endColumns": "53,66", "endOffsets": "5899,6089"}}, {"source": "D:\\AetherProject\\Aether Client\\app\\src\\main\\res\\values\\themes.xml", "from": {"startLines": "3", "startColumns": "4", "startOffsets": "56", "endColumns": "89", "endOffsets": "141"}, "to": {"startLines": "322", "startColumns": "4", "startOffsets": "20712", "endColumns": "88", "endOffsets": "20796"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\76f6aeeb7c72cabab8c29d18f4d3161d\\transformed\\core-1.16.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,100,101,105,106,107,108,114,124,159,180,213", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,115,187,275,340,406,475,538,608,676,748,818,879,953,1026,1087,1148,1210,1274,1336,1397,1465,1565,1625,1691,1764,1833,1890,1942,2004,2076,2152,2217,2276,2335,2395,2455,2515,2575,2635,2695,2755,2815,2875,2935,2994,3054,3114,3174,3234,3294,3354,3414,3474,3534,3594,3653,3713,3773,3832,3891,3950,4009,4068,4127,4162,4197,4252,4315,4370,4428,4484,4542,4603,4666,4723,4774,4832,4882,4943,5000,5066,5100,5135,5170,5240,5307,5379,5448,5517,5591,5663,5751,5822,5939,6140,6250,6451,6580,6652,6719,6922,7223,9029,9710,10392", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,99,100,104,105,106,107,113,123,158,179,212,218", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,34,34,54,62,54,57,55,57,60,62,56,50,57,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "110,182,270,335,401,470,533,603,671,743,813,874,948,1021,1082,1143,1205,1269,1331,1392,1460,1560,1620,1686,1759,1828,1885,1937,1999,2071,2147,2212,2271,2330,2390,2450,2510,2570,2630,2690,2750,2810,2870,2930,2989,3049,3109,3169,3229,3289,3349,3409,3469,3529,3589,3648,3708,3768,3827,3886,3945,4004,4063,4122,4157,4192,4247,4310,4365,4423,4479,4537,4598,4661,4718,4769,4827,4877,4938,4995,5061,5095,5130,5165,5235,5302,5374,5443,5512,5586,5658,5746,5817,5934,6135,6245,6446,6575,6647,6714,6917,7218,9024,9705,10387,10554"}, "to": {"startLines": "2,26,27,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,97,98,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,127,138,139,140,141,142,143,144,273,311,312,316,317,321,323,324,332,338,348,383,404,437", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,1419,1491,1639,1704,1770,1839,1902,1972,2040,2112,2182,2243,2317,2390,2451,2512,2574,2638,2700,2761,2829,2929,2989,3055,3128,3197,3254,3306,3368,3440,3516,3581,3640,3699,3759,3819,3879,3939,3999,4059,4119,4179,4239,4299,4358,4418,4478,4538,4598,4658,4718,4778,4838,4898,4958,5017,5077,5137,5196,5255,5314,5373,5432,5904,5939,6136,6191,6254,6309,6367,6423,6481,6542,6605,6662,6713,6771,6821,6882,6939,7005,7039,7074,7485,8203,8270,8342,8411,8480,8554,8626,17723,19954,20071,20272,20382,20583,20801,20873,21244,21447,21748,23554,24235,24917", "endLines": "2,26,27,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,97,98,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,127,138,139,140,141,142,143,144,273,311,315,316,320,321,323,324,337,347,382,403,436,442", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,34,34,54,62,54,57,55,57,60,62,56,50,57,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "205,1486,1574,1699,1765,1834,1897,1967,2035,2107,2177,2238,2312,2385,2446,2507,2569,2633,2695,2756,2824,2924,2984,3050,3123,3192,3249,3301,3363,3435,3511,3576,3635,3694,3754,3814,3874,3934,3994,4054,4114,4174,4234,4294,4353,4413,4473,4533,4593,4653,4713,4773,4833,4893,4953,5012,5072,5132,5191,5250,5309,5368,5427,5486,5934,5969,6186,6249,6304,6362,6418,6476,6537,6600,6657,6708,6766,6816,6877,6934,7000,7034,7069,7104,7550,8265,8337,8406,8475,8549,8621,8709,17789,20066,20267,20377,20578,20707,20868,20935,21442,21743,23549,24230,24912,25079"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\b3f17a82ac0ca43054f5ba6dd1a02314\\transformed\\savedstate-release\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "53", "endOffsets": "104"}, "to": {"startLines": "123", "startColumns": "4", "startOffsets": "7255", "endColumns": "53", "endOffsets": "7304"}}, {"source": "D:\\AetherProject\\Aether Client\\app\\src\\main\\res\\values\\colors.xml", "from": {"startLines": "9,10,13,14,34,35,28,26,29,27,21,19,20,22,23,5,6,7,8,32,15,16,33,2", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "376,424,523,575,1425,1472,1193,1088,1244,1140,884,778,830,939,991,160,209,266,317,1327,635,684,1376,55", "endColumns": "47,55,51,59,46,45,50,51,50,52,54,51,53,51,59,48,56,50,58,48,48,56,48,59", "endOffsets": "419,475,570,630,1467,1513,1239,1135,1290,1188,934,825,879,986,1046,204,261,312,371,1371,679,736,1420,110"}, "to": {"startLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,28", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "210,258,314,366,426,473,519,570,622,673,726,781,833,887,939,999,1048,1105,1156,1215,1264,1313,1370,1579", "endColumns": "47,55,51,59,46,45,50,51,50,52,54,51,53,51,59,48,56,50,58,48,48,56,48,59", "endOffsets": "253,309,361,421,468,514,565,617,668,721,776,828,882,934,994,1043,1100,1151,1210,1259,1308,1365,1414,1634"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\13ceaa855c6ae6b23241ae42f3bae2bf\\transformed\\material3-release\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,15,16,17,18,19,20,21,22,23,24,25,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,74", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,173,261,347,428,512,581,646,729,835,921,1041,1095,1164,1225,1294,1383,1478,1552,1649,1742,1840,1989,2080,2168,2264,2362,2426,2494,2581,2675,2742,2814,2886,2987,3096,3172,3241,3289,3355,3419,3493,3550,3607,3679,3729,3783,3854,3925,3995,4064,4122,4198,4269,4343,4429,4479,4549,4614,5329", "endLines": "2,3,4,5,6,7,8,9,10,11,14,15,16,17,18,19,20,21,22,23,24,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,73,76", "endColumns": "72,87,85,80,83,68,64,82,105,85,13,53,68,60,68,88,94,73,96,92,97,13,90,87,95,97,63,67,86,93,66,71,71,100,108,75,68,47,65,63,73,56,56,71,49,53,70,70,69,68,57,75,70,73,85,49,69,64,12,12", "endOffsets": "168,256,342,423,507,576,641,724,830,916,1036,1090,1159,1220,1289,1378,1473,1547,1644,1737,1835,1984,2075,2163,2259,2357,2421,2489,2576,2670,2737,2809,2881,2982,3091,3167,3236,3284,3350,3414,3488,3545,3602,3674,3724,3778,3849,3920,3990,4059,4117,4193,4264,4338,4424,4474,4544,4609,5324,5477"}, "to": {"startLines": "126,175,176,177,178,179,180,181,182,183,184,187,188,189,190,191,192,193,194,195,196,197,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,290,300", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "7412,10950,11038,11124,11205,11289,11358,11423,11506,11612,11698,11818,11872,11941,12002,12071,12160,12255,12329,12426,12519,12617,12766,12857,12945,13041,13139,13203,13271,13358,13452,13519,13591,13663,13764,13873,13949,14018,14066,14132,14196,14270,14327,14384,14456,14506,14560,14631,14702,14772,14841,14899,14975,15046,15120,15206,15256,15326,18718,19433", "endLines": "126,175,176,177,178,179,180,181,182,183,186,187,188,189,190,191,192,193,194,195,196,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,299,302", "endColumns": "72,87,85,80,83,68,64,82,105,85,13,53,68,60,68,88,94,73,96,92,97,13,90,87,95,97,63,67,86,93,66,71,71,100,108,75,68,47,65,63,73,56,56,71,49,53,70,70,69,68,57,75,70,73,85,49,69,64,12,12", "endOffsets": "7480,11033,11119,11200,11284,11353,11418,11501,11607,11693,11813,11867,11936,11997,12066,12155,12250,12324,12421,12514,12612,12761,12852,12940,13036,13134,13198,13266,13353,13447,13514,13586,13658,13759,13868,13944,14013,14061,14127,14191,14265,14322,14379,14451,14501,14555,14626,14697,14767,14836,14894,14970,15041,15115,15201,15251,15321,15386,19428,19581"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\52b211d17365fa8559962d9ffecdb379\\transformed\\navigation-common-release\\res\\values\\values.xml", "from": {"startLines": "2,15,21,27,30", "startColumns": "4,4,4,4,4", "startOffsets": "55,694,938,1185,1318", "endLines": "14,20,26,29,34", "endColumns": "24,24,24,24,24", "endOffsets": "689,933,1180,1313,1495"}, "to": {"startLines": "443,456,462,468,477", "startColumns": "4,4,4,4,4", "startOffsets": "25084,25723,25967,26214,26577", "endLines": "455,461,467,470,481", "endColumns": "24,24,24,24,24", "endOffsets": "25718,25962,26209,26342,26754"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\c0b5525281ff7443948eefee08cdea8d\\transformed\\core-viewtree-1.0.0\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "42", "endOffsets": "93"}, "to": {"startLines": "120", "startColumns": "4", "startOffsets": "7109", "endColumns": "42", "endOffsets": "7147"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\3993c5e4f0652e0e7d9589210d919205\\transformed\\lifecycle-viewmodel-release\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "49", "endOffsets": "100"}, "to": {"startLines": "124", "startColumns": "4", "startOffsets": "7309", "endColumns": "49", "endOffsets": "7354"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\6277c10fdac6864aa79dc40a9be66d06\\transformed\\navigation-runtime-release\\res\\values\\values.xml", "from": {"startLines": "2,3,10,13", "startColumns": "4,4,4,4", "startOffsets": "55,108,412,527", "endLines": "2,9,12,15", "endColumns": "52,24,24,24", "endOffsets": "103,407,522,637"}, "to": {"startLines": "99,325,471,474", "startColumns": "4,4,4,4", "startOffsets": "5974,20940,26347,26462", "endLines": "99,331,473,476", "endColumns": "52,24,24,24", "endOffsets": "6022,21239,26457,26572"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\7dda5bceb85b19f16161f75eb227258f\\transformed\\lifecycle-runtime-release\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "42", "endOffsets": "93"}, "to": {"startLines": "121", "startColumns": "4", "startOffsets": "7152", "endColumns": "42", "endOffsets": "7190"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\d61819a9c261b5407a6ee59c046430b9\\transformed\\ui-release\\res\\values\\values.xml", "from": {"startLines": "34,35,36,37,38,39,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,63,66", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2060,2134,2192,2247,2298,2353,2452,2517,2571,2637,2738,2796,2848,2908,2970,3024,3074,3128,3174,3228,3274,3316,3356,3403,3439,3529,3641,3752", "endLines": "34,35,36,37,38,39,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,62,65,70", "endColumns": "73,57,54,50,54,52,64,53,65,100,57,51,59,61,53,49,53,45,53,45,41,39,46,35,89,12,12,12", "endOffsets": "2129,2187,2242,2293,2348,2401,2512,2566,2632,2733,2791,2843,2903,2965,3019,3069,3123,3169,3223,3269,3311,3351,3398,3434,3524,3636,3747,4004"}, "to": {"startLines": "90,91,92,94,95,125,150,151,156,157,159,170,171,239,241,252,253,261,268,270,271,272,275,276,277,287,303,306", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5491,5565,5623,5744,5795,7359,9141,9206,9446,9512,9655,10388,10440,15540,15664,16496,16546,17127,17491,17595,17641,17683,17832,17879,17915,18606,19586,19697", "endLines": "90,91,92,94,95,125,150,151,156,157,159,170,171,239,241,252,253,261,268,270,271,272,275,276,277,289,305,310", "endColumns": "73,57,54,50,54,52,64,53,65,100,57,51,59,61,53,49,53,45,53,45,41,39,46,35,89,12,12,12", "endOffsets": "5560,5618,5673,5790,5845,7407,9201,9255,9507,9608,9708,10435,10495,15597,15713,16541,16595,17168,17540,17636,17678,17718,17874,17910,18000,18713,19692,19949"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\c5ff8f75be78b3ee3473720100b9266a\\transformed\\activity-1.10.1\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,97", "endColumns": "41,59", "endOffsets": "92,152"}, "to": {"startLines": "101,122", "startColumns": "4,4", "startOffsets": "6094,7195", "endColumns": "41,59", "endOffsets": "6131,7250"}}, {"source": "D:\\AetherProject\\Aether Client\\app\\src\\main\\res\\values\\strings.xml", "from": {"startLines": "41,33,34,1,9,25,27,10,20,64,13,24,62,45,50,19,63,36,65,32,35,67,30,18,39,6,43,60,8,66,44,11,49,46,29,16,57,58,53,54,51,56,55,15,48,12,69,59,17,37,28,31,40,21,22,42,61,52,26,14,5,4,23,38,47,7,68", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2721,2321,2365,16,683,1806,1925,752,1505,4364,998,1699,4231,3072,3282,1461,4306,2491,4490,2244,2417,4585,2114,1405,2621,374,2807,4076,467,4532,2891,857,3244,3114,2052,1193,3777,3847,3460,3514,3324,3669,3599,1113,3198,926,4729,3955,1283,2533,2000,2170,2678,1547,1609,2761,4185,3391,1875,1075,203,127,1661,2575,3156,412,4667", "endColumns": "39,43,51,64,68,68,74,104,41,125,76,106,74,41,41,43,57,41,41,76,73,81,55,55,56,37,83,108,215,52,180,68,37,41,61,89,69,107,53,84,66,107,69,79,45,71,43,120,121,41,51,73,42,61,51,45,45,68,49,37,170,75,37,45,41,54,61", "endOffsets": "2756,2360,2412,76,747,1870,1995,852,1542,4485,1070,1801,4301,3109,3319,1500,4359,2528,4527,2316,2486,4662,2165,1456,2673,407,2886,4180,678,4580,3067,921,3277,3151,2109,1278,3842,3950,3509,3594,3386,3772,3664,1188,3239,993,4768,4071,1400,2570,2047,2239,2716,1604,1656,2802,4226,3455,1920,1108,369,198,1694,2616,3193,462,4724"}, "to": {"startLines": "128,129,130,132,134,135,136,137,145,146,147,148,149,152,153,154,155,158,160,161,162,163,164,165,166,167,168,169,172,173,174,236,237,238,240,242,243,244,245,246,247,248,249,250,251,254,255,256,257,258,259,260,262,263,264,265,266,267,269,274,278,279,280,283,284,285,286", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "7555,7595,7639,7774,7885,7954,8023,8098,8714,8756,8882,8959,9066,9260,9302,9344,9388,9613,9713,9755,9832,9906,9988,10044,10100,10157,10195,10279,10500,10716,10769,15391,15460,15498,15602,15718,15808,15878,15986,16040,16125,16192,16300,16370,16450,16600,16672,16716,16837,16959,17001,17053,17173,17216,17278,17330,17376,17422,17545,17794,18005,18176,18252,18401,18447,18489,18544", "endColumns": "39,43,51,64,68,68,74,104,41,125,76,106,74,41,41,43,57,41,41,76,73,81,55,55,56,37,83,108,215,52,180,68,37,41,61,89,69,107,53,84,66,107,69,79,45,71,43,120,121,41,51,73,42,61,51,45,45,68,49,37,170,75,37,45,41,54,61", "endOffsets": "7590,7634,7686,7834,7949,8018,8093,8198,8751,8877,8954,9061,9136,9297,9339,9383,9441,9650,9750,9827,9901,9983,10039,10095,10152,10190,10274,10383,10711,10764,10945,15455,15493,15535,15659,15803,15873,15981,16035,16120,16187,16295,16365,16445,16491,16667,16711,16832,16954,16996,17048,17122,17211,17273,17325,17371,17417,17486,17590,17827,18171,18247,18285,18442,18484,18539,18601"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\c48ba905499e1ba3d2fda11192f445a7\\transformed\\ui-graphics-release\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "65", "endOffsets": "116"}, "to": {"startLines": "93", "startColumns": "4", "startOffsets": "5678", "endColumns": "65", "endOffsets": "5739"}}]}]}