<?xml version="1.0" encoding="UTF-8"?>
<testsuite name="com.radiantbyte.aetherproxy.version.VersionMapperTest" tests="8" skipped="0" failures="4" errors="0" timestamp="2025-07-28T18:35:58" hostname="DESKTOP-G21U0DT" time="0.711">
  <properties/>
  <testcase name="testIsVersionSupported()" classname="com.radiantbyte.aetherproxy.version.VersionMapperTest" time="0.702">
    <failure message="org.opentest4j.AssertionFailedError: expected: &lt;true&gt; but was: &lt;false&gt;" type="org.opentest4j.AssertionFailedError">org.opentest4j.AssertionFailedError: expected: &lt;true&gt; but was: &lt;false&gt;
	at app//org.junit.jupiter.api.AssertionFailureBuilder.build(AssertionFailureBuilder.java:151)
	at app//org.junit.jupiter.api.AssertionFailureBuilder.buildAndThrow(AssertionFailureBuilder.java:132)
	at app//org.junit.jupiter.api.AssertTrue.failNotTrue(AssertTrue.java:63)
	at app//org.junit.jupiter.api.AssertTrue.assertTrue(AssertTrue.java:36)
	at app//org.junit.jupiter.api.AssertTrue.assertTrue(AssertTrue.java:31)
	at app//org.junit.jupiter.api.Assertions.assertTrue(Assertions.java:183)
	at app//com.radiantbyte.aetherproxy.version.VersionMapperTest.testIsVersionSupported(VersionMapperTest.kt:40)
	at java.base@21.0.6/java.lang.reflect.Method.invoke(Method.java:580)
	at java.base@21.0.6/java.util.ArrayList.forEach(ArrayList.java:1596)
	at java.base@21.0.6/java.util.ArrayList.forEach(ArrayList.java:1596)
</failure>
  </testcase>
  <testcase name="testGetSupportedVersions()" classname="com.radiantbyte.aetherproxy.version.VersionMapperTest" time="0.002">
    <failure message="org.opentest4j.AssertionFailedError: expected: &lt;true&gt; but was: &lt;false&gt;" type="org.opentest4j.AssertionFailedError">org.opentest4j.AssertionFailedError: expected: &lt;true&gt; but was: &lt;false&gt;
	at app//org.junit.jupiter.api.AssertionFailureBuilder.build(AssertionFailureBuilder.java:151)
	at app//org.junit.jupiter.api.AssertionFailureBuilder.buildAndThrow(AssertionFailureBuilder.java:132)
	at app//org.junit.jupiter.api.AssertTrue.failNotTrue(AssertTrue.java:63)
	at app//org.junit.jupiter.api.AssertTrue.assertTrue(AssertTrue.java:36)
	at app//org.junit.jupiter.api.AssertTrue.assertTrue(AssertTrue.java:31)
	at app//org.junit.jupiter.api.Assertions.assertTrue(Assertions.java:183)
	at app//com.radiantbyte.aetherproxy.version.VersionMapperTest.testGetSupportedVersions(VersionMapperTest.kt:68)
	at java.base@21.0.6/java.lang.reflect.Method.invoke(Method.java:580)
	at java.base@21.0.6/java.util.ArrayList.forEach(ArrayList.java:1596)
	at java.base@21.0.6/java.util.ArrayList.forEach(ArrayList.java:1596)
</failure>
  </testcase>
  <testcase name="testUnknownVersionFallback()" classname="com.radiantbyte.aetherproxy.version.VersionMapperTest" time="0.002"/>
  <testcase name="testGetProtocolForVersion()" classname="com.radiantbyte.aetherproxy.version.VersionMapperTest" time="0.001">
    <failure message="org.opentest4j.AssertionFailedError: expected: &lt;818&gt; but was: &lt;819&gt;" type="org.opentest4j.AssertionFailedError">org.opentest4j.AssertionFailedError: expected: &lt;818&gt; but was: &lt;819&gt;
	at app//org.junit.jupiter.api.AssertionFailureBuilder.build(AssertionFailureBuilder.java:151)
	at app//org.junit.jupiter.api.AssertionFailureBuilder.buildAndThrow(AssertionFailureBuilder.java:132)
	at app//org.junit.jupiter.api.AssertEquals.failNotEqual(AssertEquals.java:197)
	at app//org.junit.jupiter.api.AssertEquals.assertEquals(AssertEquals.java:150)
	at app//org.junit.jupiter.api.AssertEquals.assertEquals(AssertEquals.java:145)
	at app//org.junit.jupiter.api.Assertions.assertEquals(Assertions.java:531)
	at app//com.radiantbyte.aetherproxy.version.VersionMapperTest.testGetProtocolForVersion(VersionMapperTest.kt:34)
	at java.base@21.0.6/java.lang.reflect.Method.invoke(Method.java:580)
	at java.base@21.0.6/java.util.ArrayList.forEach(ArrayList.java:1596)
	at java.base@21.0.6/java.util.ArrayList.forEach(ArrayList.java:1596)
</failure>
  </testcase>
  <testcase name="testIsProtocolSupported()" classname="com.radiantbyte.aetherproxy.version.VersionMapperTest" time="0.001"/>
  <testcase name="testGetCodecForVersion()" classname="com.radiantbyte.aetherproxy.version.VersionMapperTest" time="0.001">
    <failure message="org.opentest4j.AssertionFailedError: expected: &lt;818&gt; but was: &lt;819&gt;" type="org.opentest4j.AssertionFailedError">org.opentest4j.AssertionFailedError: expected: &lt;818&gt; but was: &lt;819&gt;
	at app//org.junit.jupiter.api.AssertionFailureBuilder.build(AssertionFailureBuilder.java:151)
	at app//org.junit.jupiter.api.AssertionFailureBuilder.buildAndThrow(AssertionFailureBuilder.java:132)
	at app//org.junit.jupiter.api.AssertEquals.failNotEqual(AssertEquals.java:197)
	at app//org.junit.jupiter.api.AssertEquals.assertEquals(AssertEquals.java:150)
	at app//org.junit.jupiter.api.AssertEquals.assertEquals(AssertEquals.java:145)
	at app//org.junit.jupiter.api.Assertions.assertEquals(Assertions.java:531)
	at app//com.radiantbyte.aetherproxy.version.VersionMapperTest.testGetCodecForVersion(VersionMapperTest.kt:15)
	at java.base@21.0.6/java.lang.reflect.Method.invoke(Method.java:580)
	at java.base@21.0.6/java.util.ArrayList.forEach(ArrayList.java:1596)
	at java.base@21.0.6/java.util.ArrayList.forEach(ArrayList.java:1596)
</failure>
  </testcase>
  <testcase name="testGetCodecForProtocol()" classname="com.radiantbyte.aetherproxy.version.VersionMapperTest" time="0.001"/>
  <testcase name="testGetSupportedProtocols()" classname="com.radiantbyte.aetherproxy.version.VersionMapperTest" time="0.0"/>
  <system-out><![CDATA[[00:35:59 DEBUG]: Using Log4J2 as the default logging framework
[00:35:59 DEBUG]: -Dio.netty.noUnsafe: false
[00:35:59 DEBUG]: Java version: 21
[00:35:59 DEBUG]: sun.misc.Unsafe.theUnsafe: available
[00:35:59 DEBUG]: sun.misc.Unsafe base methods: all available
[00:35:59 DEBUG]: java.nio.Buffer.address: available
[00:35:59 DEBUG]: direct buffer constructor: unavailable: Reflective setAccessible(true) disabled
[00:35:59 DEBUG]: java.nio.Bits.unaligned: available, true
[00:35:59 DEBUG]: jdk.internal.misc.Unsafe.allocateUninitializedArray(int): unavailable: symbolic reference class is not accessible: class jdk.internal.misc.Unsafe, from class io.netty.util.internal.PlatformDependent0 (unnamed module @4efc180e)
[00:35:59 DEBUG]: java.nio.DirectByteBuffer.<init>(long, {int,long}): unavailable
[00:35:59 DEBUG]: sun.misc.Unsafe: available
[00:35:59 DEBUG]: -Dio.netty.tmpdir: C:\Users\<USER>\AppData\Local\Temp (java.io.tmpdir)
[00:35:59 DEBUG]: -Dio.netty.bitMode: 64 (sun.arch.data.model)
[00:35:59 DEBUG]: Platform: Windows
[00:35:59 DEBUG]: -Dio.netty.maxDirectMemory: -1 bytes
[00:35:59 DEBUG]: java.nio.ByteBuffer.cleaner(): available
[00:35:59 DEBUG]: -Dio.netty.noPreferDirect: false
VersionMapper: Unknown Minecraft version 'unknown.version', using default codec
VersionMapper: Unknown Minecraft version 'unknown.version', using default protocol
VersionMapper: Unknown Minecraft version '1.21.93', using default protocol
VersionMapper: Unknown Minecraft version '1.21.94', using default protocol
VersionMapper: Unknown Minecraft version '1.21.90', using default protocol
VersionMapper: Unknown Minecraft version '1.21.93', using default codec
VersionMapper: Unknown Minecraft version '1.21.90', using default codec
]]></system-out>
  <system-err><![CDATA[SLF4J(W): No SLF4J providers were found.
SLF4J(W): Defaulting to no-operation (NOP) logger implementation
SLF4J(W): See https://www.slf4j.org/codes.html#noProviders for further details.
]]></system-err>
</testsuite>
