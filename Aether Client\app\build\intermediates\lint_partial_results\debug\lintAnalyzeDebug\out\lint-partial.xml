<?xml version="1.0" encoding="UTF-8"?>
<incidents format="6" by="lint 8.11.1" type="partial_results">
    <map id="UnsafeImplicitIntentLaunch">
            <map id="actionsSent">
                    <map id="android.intent.action.VIEW (used to start an activity)">
                        <location id="0"
                            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/radiantbyte/aetherclient/router/main/AboutPage.kt"
                            line="148"
                            column="34"
                            startOffset="5278"
                            endLine="149"
                            endColumn="77"
                            endOffset="5381"/>
                        <location id="1"
                            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/radiantbyte/aetherclient/router/main/AboutPage.kt"
                            line="180"
                            column="34"
                            startOffset="6602"
                            endLine="180"
                            endColumn="101"
                            endOffset="6669"/>
                        <location id="2"
                            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/radiantbyte/aetherclient/router/main/AboutPage.kt"
                            line="408"
                            column="38"
                            startOffset="15191"
                            endLine="409"
                            endColumn="91"
                            endOffset="15308"/>
                    </map>
                    <map id="android.settings.REQUEST_IGNORE_BATTERY_OPTIMIZATIONS (used to start an activity)">
                        <location id="0"
                            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/radiantbyte/aetherclient/app/AetherActivity.kt"
                            line="126"
                            column="17"
                            startOffset="4746"
                            endLine="126"
                            endColumn="78"
                            endOffset="4807"/>
                    </map>
            </map>
    </map>
    <map id="UnsafeIntentLaunch">
            <map id="unprotected">
                <entry
                    name="com.radiantbyte.aetherclient.app.AetherActivity"
                    boolean="true"/>
            </map>
    </map>
    <map id="UnusedResources">
        <location id="R.color.aether_accent"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="10"
            column="12"
            startOffset="383"
            endLine="10"
            endColumn="32"
            endOffset="403"/>
        <location id="R.color.aether_accent_variant"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="11"
            column="12"
            startOffset="431"
            endLine="11"
            endColumn="40"
            endOffset="459"/>
        <location id="R.color.aether_background"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="14"
            column="12"
            startOffset="530"
            endLine="14"
            endColumn="36"
            endOffset="554"/>
        <location id="R.color.aether_background_variant"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="15"
            column="12"
            startOffset="582"
            endLine="15"
            endColumn="44"
            endOffset="614"/>
        <location id="R.color.aether_error"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="35"
            column="12"
            startOffset="1432"
            endLine="35"
            endColumn="31"
            endOffset="1451"/>
        <location id="R.color.aether_info"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="36"
            column="12"
            startOffset="1479"
            endLine="36"
            endColumn="30"
            endOffset="1497"/>
        <location id="R.color.aether_neon_blue"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="29"
            column="12"
            startOffset="1200"
            endLine="29"
            endColumn="35"
            endOffset="1223"/>
        <location id="R.color.aether_neon_green"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="27"
            column="12"
            startOffset="1095"
            endLine="27"
            endColumn="36"
            endOffset="1119"/>
        <location id="R.color.aether_neon_pink"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="30"
            column="12"
            startOffset="1251"
            endLine="30"
            endColumn="35"
            endOffset="1274"/>
        <location id="R.color.aether_neon_purple"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="28"
            column="12"
            startOffset="1147"
            endLine="28"
            endColumn="37"
            endOffset="1172"/>
        <location id="R.color.aether_on_background"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="22"
            column="12"
            startOffset="891"
            endLine="22"
            endColumn="39"
            endOffset="918"/>
        <location id="R.color.aether_on_primary"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="20"
            column="12"
            startOffset="785"
            endLine="20"
            endColumn="36"
            endOffset="809"/>
        <location id="R.color.aether_on_secondary"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="21"
            column="12"
            startOffset="837"
            endLine="21"
            endColumn="38"
            endOffset="863"/>
        <location id="R.color.aether_on_surface"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="23"
            column="12"
            startOffset="946"
            endLine="23"
            endColumn="36"
            endOffset="970"/>
        <location id="R.color.aether_on_surface_variant"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="24"
            column="12"
            startOffset="998"
            endLine="24"
            endColumn="44"
            endOffset="1030"/>
        <location id="R.color.aether_primary"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="6"
            column="12"
            startOffset="167"
            endLine="6"
            endColumn="33"
            endOffset="188"/>
        <location id="R.color.aether_primary_variant"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="7"
            column="12"
            startOffset="216"
            endLine="7"
            endColumn="41"
            endOffset="245"/>
        <location id="R.color.aether_secondary"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="8"
            column="12"
            startOffset="273"
            endLine="8"
            endColumn="35"
            endOffset="296"/>
        <location id="R.color.aether_secondary_variant"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="9"
            column="12"
            startOffset="324"
            endLine="9"
            endColumn="43"
            endOffset="355"/>
        <location id="R.color.aether_success"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="33"
            column="12"
            startOffset="1334"
            endLine="33"
            endColumn="33"
            endOffset="1355"/>
        <location id="R.color.aether_surface"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="16"
            column="12"
            startOffset="642"
            endLine="16"
            endColumn="33"
            endOffset="663"/>
        <location id="R.color.aether_surface_variant"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="17"
            column="12"
            startOffset="691"
            endLine="17"
            endColumn="41"
            endOffset="720"/>
        <location id="R.color.aether_warning"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="34"
            column="12"
            startOffset="1383"
            endLine="34"
            endColumn="33"
            endOffset="1404"/>
        <location id="R.color.app_icon_background_color"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="3"
            column="12"
            startOffset="62"
            endLine="3"
            endColumn="44"
            endOffset="94"/>
        <location id="R.string.about"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="42"
            column="13"
            startOffset="2729"
            endLine="42"
            endColumn="25"
            endOffset="2741"/>
        <location id="R.string.account"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="34"
            column="13"
            startOffset="2329"
            endLine="34"
            endColumn="27"
            endOffset="2343"/>
        <location id="R.string.add_account"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="35"
            column="13"
            startOffset="2373"
            endLine="35"
            endColumn="31"
            endOffset="2391"/>
        <location id="R.string.backend"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="10"
            column="13"
            startOffset="691"
            endLine="10"
            endColumn="27"
            endOffset="705"/>
        <location id="R.string.backend_connected"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="26"
            column="13"
            startOffset="1814"
            endLine="26"
            endColumn="37"
            endOffset="1838"/>
        <location id="R.string.backend_disconnected"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="28"
            column="13"
            startOffset="1933"
            endLine="28"
            endColumn="40"
            endOffset="1960"/>
        <location id="R.string.backend_introduction"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="11"
            column="13"
            startOffset="760"
            endLine="11"
            endColumn="40"
            endOffset="787"/>
        <location id="R.string.cancel"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="21"
            column="13"
            startOffset="1513"
            endLine="21"
            endColumn="26"
            endOffset="1526"/>
        <location id="R.string.cannot_back"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="65"
            column="13"
            startOffset="4372"
            endLine="65"
            endColumn="31"
            endOffset="4390"/>
        <location id="R.string.capturing_game_packets"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="14"
            column="13"
            startOffset="1006"
            endLine="14"
            endColumn="42"
            endOffset="1035"/>
        <location id="R.string.change_game_settings_tip"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="25"
            column="13"
            startOffset="1707"
            endLine="25"
            endColumn="44"
            endOffset="1738"/>
        <location id="R.string.check_updates_and_news"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="63"
            column="13"
            startOffset="4239"
            endLine="63"
            endColumn="42"
            endOffset="4268"/>
        <location id="R.string.combat"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="46"
            column="13"
            startOffset="3080"
            endLine="46"
            endColumn="26"
            endOffset="3093"/>
        <location id="R.string.config"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="51"
            column="13"
            startOffset="3290"
            endLine="51"
            endColumn="26"
            endOffset="3303"/>
        <location id="R.string.confirm"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="20"
            column="13"
            startOffset="1469"
            endLine="20"
            endColumn="27"
            endOffset="1483"/>
        <location id="R.string.crash_happened"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="64"
            column="13"
            startOffset="4314"
            endLine="64"
            endColumn="34"
            endOffset="4335"/>
        <location id="R.string.delete"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="37"
            column="13"
            startOffset="2499"
            endLine="37"
            endColumn="26"
            endOffset="2512"/>
        <location id="R.string.effect"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="66"
            column="13"
            startOffset="4498"
            endLine="66"
            endColumn="26"
            endOffset="4511"/>
        <location id="R.string.failed_to_launch_game"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="33"
            column="13"
            startOffset="2252"
            endLine="33"
            endColumn="41"
            endOffset="2280"/>
        <location id="R.string.fetch_account_failed"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="36"
            column="13"
            startOffset="2425"
            endLine="36"
            endColumn="40"
            endOffset="2452"/>
        <location id="R.string.fetch_account_successfully"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="68"
            column="13"
            startOffset="4593"
            endLine="68"
            endColumn="46"
            endOffset="4626"/>
        <location id="R.string.game_selector"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="31"
            column="13"
            startOffset="2122"
            endLine="31"
            endColumn="33"
            endOffset="2142"/>
        <location id="R.string.game_settings"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="19"
            column="13"
            startOffset="1413"
            endLine="19"
            endColumn="33"
            endOffset="1433"/>
        <location id="R.string.has_been_selected"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="40"
            column="13"
            startOffset="2629"
            endLine="40"
            endColumn="37"
            endOffset="2653"/>
        <location id="R.string.home"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="7"
            column="13"
            startOffset="382"
            endLine="7"
            endColumn="24"
            endOffset="393"/>
        <location id="R.string.how_do_i_switch_login_mode"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="44"
            column="13"
            startOffset="2815"
            endLine="44"
            endColumn="46"
            endOffset="2848"/>
        <location id="R.string.ignore_battery_optimization_denied"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="61"
            column="13"
            startOffset="4084"
            endLine="61"
            endColumn="54"
            endOffset="4125"/>
        <location id="R.string.introduction"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="9"
            column="13"
            startOffset="475"
            endLine="9"
            endColumn="32"
            endOffset="494"/>
        <location id="R.string.login_in"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="67"
            column="13"
            startOffset="4540"
            endLine="67"
            endColumn="28"
            endOffset="4555"/>
        <location id="R.string.login_mode_introduction"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="45"
            column="13"
            startOffset="2899"
            endLine="45"
            endColumn="43"
            endOffset="2929"/>
        <location id="R.string.minecraft"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="12"
            column="13"
            startOffset="865"
            endLine="12"
            endColumn="29"
            endOffset="881"/>
        <location id="R.string.misc"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="50"
            column="13"
            startOffset="3252"
            endLine="50"
            endColumn="24"
            endOffset="3263"/>
        <location id="R.string.motion"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="47"
            column="13"
            startOffset="3122"
            endLine="47"
            endColumn="26"
            endOffset="3135"/>
        <location id="R.string.no_game_selected"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="30"
            column="13"
            startOffset="2060"
            endLine="30"
            endColumn="36"
            endOffset="2083"/>
        <location id="R.string.notification_permission_denied"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="17"
            column="13"
            startOffset="1201"
            endLine="17"
            endColumn="50"
            endOffset="1238"/>
        <location id="R.string.overlay_border_color"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="58"
            column="13"
            startOffset="3785"
            endLine="58"
            endColumn="40"
            endOffset="3812"/>
        <location id="R.string.overlay_border_color_description"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="59"
            column="13"
            startOffset="3855"
            endLine="59"
            endColumn="52"
            endOffset="3894"/>
        <location id="R.string.overlay_icon"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="54"
            column="13"
            startOffset="3468"
            endLine="54"
            endColumn="32"
            endOffset="3487"/>
        <location id="R.string.overlay_icon_description"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="55"
            column="13"
            startOffset="3522"
            endLine="55"
            endColumn="44"
            endOffset="3553"/>
        <location id="R.string.overlay_opacity"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="52"
            column="13"
            startOffset="3332"
            endLine="52"
            endColumn="35"
            endOffset="3354"/>
        <location id="R.string.overlay_opacity_description"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="57"
            column="13"
            startOffset="3677"
            endLine="57"
            endColumn="47"
            endOffset="3711"/>
        <location id="R.string.overlay_opacity_settings"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="56"
            column="13"
            startOffset="3607"
            endLine="56"
            endColumn="44"
            endOffset="3638"/>
        <location id="R.string.overlay_permission_denied"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="16"
            column="13"
            startOffset="1121"
            endLine="16"
            endColumn="45"
            endOffset="1153"/>
        <location id="R.string.particle"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="49"
            column="13"
            startOffset="3206"
            endLine="49"
            endColumn="28"
            endOffset="3221"/>
        <location id="R.string.recommended_version"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="13"
            column="13"
            startOffset="934"
            endLine="13"
            endColumn="39"
            endOffset="960"/>
        <location id="R.string.refresh"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="70"
            column="13"
            startOffset="4737"
            endLine="70"
            endColumn="27"
            endOffset="4751"/>
        <location id="R.string.request_ignore_battery_optimization"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="60"
            column="13"
            startOffset="3963"
            endLine="60"
            endColumn="55"
            endOffset="4005"/>
        <location id="R.string.request_overlay_permission"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="18"
            column="13"
            startOffset="1291"
            endLine="18"
            endColumn="46"
            endOffset="1324"/>
        <location id="R.string.select"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="38"
            column="13"
            startOffset="2541"
            endLine="38"
            endColumn="26"
            endOffset="2554"/>
        <location id="R.string.select_game"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="29"
            column="13"
            startOffset="2008"
            endLine="29"
            endColumn="31"
            endOffset="2026"/>
        <location id="R.string.select_game_first"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="32"
            column="13"
            startOffset="2178"
            endLine="32"
            endColumn="37"
            endOffset="2202"/>
        <location id="R.string.server"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="41"
            column="13"
            startOffset="2686"
            endLine="41"
            endColumn="26"
            endOffset="2699"/>
        <location id="R.string.server_host_name"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="22"
            column="13"
            startOffset="1555"
            endLine="22"
            endColumn="36"
            endOffset="1578"/>
        <location id="R.string.server_port"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="23"
            column="13"
            startOffset="1617"
            endLine="23"
            endColumn="31"
            endOffset="1635"/>
        <location id="R.string.settings"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="43"
            column="13"
            startOffset="2769"
            endLine="43"
            endColumn="28"
            endOffset="2784"/>
        <location id="R.string.shortcut"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="62"
            column="13"
            startOffset="4193"
            endLine="62"
            endColumn="28"
            endOffset="4208"/>
        <location id="R.string.shortcut_opacity"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="53"
            column="13"
            startOffset="3399"
            endLine="53"
            endColumn="36"
            endOffset="3422"/>
        <location id="R.string.start_game"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="27"
            column="13"
            startOffset="1883"
            endLine="27"
            endColumn="30"
            endOffset="1900"/>
        <location id="R.string.stop"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="15"
            column="13"
            startOffset="1083"
            endLine="15"
            endColumn="24"
            endOffset="1094"/>
        <location id="R.string.third_party_licenses_description"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="6"
            column="13"
            startOffset="211"
            endLine="6"
            endColumn="52"
            endOffset="250"/>
        <location id="R.string.third_party_licenses_title"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="5"
            column="13"
            startOffset="135"
            endLine="5"
            endColumn="46"
            endOffset="168"/>
        <location id="R.string.tips"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="24"
            column="13"
            startOffset="1669"
            endLine="24"
            endColumn="24"
            endOffset="1680"/>
        <location id="R.string.unselect"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="39"
            column="13"
            startOffset="2583"
            endLine="39"
            endColumn="28"
            endOffset="2598"/>
        <location id="R.string.visual"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="48"
            column="13"
            startOffset="3164"
            endLine="48"
            endColumn="26"
            endOffset="3177"/>
        <location id="R.string.what_is_this"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="8"
            column="13"
            startOffset="420"
            endLine="8"
            endColumn="32"
            endOffset="439"/>
        <location id="R.string.xbox_device_code"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="69"
            column="13"
            startOffset="4675"
            endLine="69"
            endColumn="36"
            endOffset="4698"/>
        <entry
            name="model"
            string="color[app_icon_background_color(D),aether_primary(D),aether_primary_variant(D),aether_secondary(D),aether_secondary_variant(D),aether_accent(D),aether_accent_variant(D),aether_background(D),aether_background_variant(D),aether_surface(D),aether_surface_variant(D),aether_on_primary(D),aether_on_secondary(D),aether_on_background(D),aether_on_surface(D),aether_on_surface_variant(D),aether_neon_green(D),aether_neon_purple(D),aether_neon_blue(D),aether_neon_pink(D),aether_success(D),aether_warning(D),aether_error(D),aether_info(D)],drawable[ic_launcher(U),ic_launcher_background(U)],mipmap[ic_launcher(U),ic_launcher_round(U),ic_launcher_foreground(U)],string[app_name(U),third_party_licenses_title(D),third_party_licenses_description(D),home(D),what_is_this(D),introduction(D),backend(D),backend_introduction(D),minecraft(D),recommended_version(D),capturing_game_packets(D),stop(D),overlay_permission_denied(D),notification_permission_denied(D),request_overlay_permission(D),game_settings(D),confirm(D),cancel(D),server_host_name(D),server_port(D),tips(D),change_game_settings_tip(D),backend_connected(D),start_game(D),backend_disconnected(D),select_game(D),no_game_selected(D),game_selector(D),select_game_first(D),failed_to_launch_game(D),account(D),add_account(D),fetch_account_failed(D),delete(D),select(D),unselect(D),has_been_selected(D),server(D),about(D),settings(D),how_do_i_switch_login_mode(D),login_mode_introduction(D),combat(D),motion(D),visual(D),particle(D),misc(D),config(D),overlay_opacity(D),shortcut_opacity(D),overlay_icon(D),overlay_icon_description(D),overlay_opacity_settings(D),overlay_opacity_description(D),overlay_border_color(D),overlay_border_color_description(D),request_ignore_battery_optimization(D),ignore_battery_optimization_denied(D),shortcut(D),check_updates_and_news(D),crash_happened(D),cannot_back(D),effect(D),login_in(D),fetch_account_successfully(D),xbox_device_code(D),refresh(D)],style[Theme_AetherClient(U)],xml[network_security_config(U),file_paths(U)];1a^19^1c,1b^19^1c;;;"/>
    </map>

</incidents>
