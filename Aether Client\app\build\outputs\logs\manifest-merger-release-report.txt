-- Merging decision tree log ---
provider#androidx.core.content.FileProvider
INJECTED from D:\AetherMCya\Aether Client\app\src\main\AndroidManifest.xml:69:9-77:20
	android:grantUriPermissions
		ADDED from D:\AetherMCya\Aether Client\app\src\main\AndroidManifest.xml:73:13-47
	android:authorities
		INJECTED from D:\AetherMCya\Aether Client\app\src\main\AndroidManifest.xml
		ADDED from D:\AetherMCya\Aether Client\app\src\main\AndroidManifest.xml:71:13-60
	android:exported
		ADDED from D:\AetherMCya\Aether Client\app\src\main\AndroidManifest.xml:72:13-37
	android:name
		ADDED from D:\AetherMCya\Aether Client\app\src\main\AndroidManifest.xml:70:13-62
manifest
ADDED from D:\AetherMCya\Aether Client\app\src\main\AndroidManifest.xml:2:1-81:12
INJECTED from D:\AetherMCya\Aether Client\app\src\main\AndroidManifest.xml:2:1-81:12
INJECTED from D:\AetherMCya\Aether Client\app\src\main\AndroidManifest.xml:2:1-81:12
INJECTED from D:\AetherMCya\Aether Client\app\src\main\AndroidManifest.xml:2:1-81:12
MERGED from [androidx.navigation:navigation-runtime-android:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\6277c10fdac6864aa79dc40a9be66d06\transformed\navigation-runtime-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-common-android:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\52b211d17365fa8559962d9ffecdb379\transformed\navigation-common-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-compose-android:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3c48004821621421fbc139ed55d36616\transformed\navigation-compose-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity:1.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\c5ff8f75be78b3ee3473720100b9266a\transformed\activity-1.10.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.material3:material3-android:1.3.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\13ceaa855c6ae6b23241ae42f3bae2bf\transformed\material3-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.animation:animation-core-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\375157c509cb07e6f50b2416596e3f39\transformed\animation-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material:material-ripple-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\0f71de82152f5b1cd2445574a26fca79\transformed\material-ripple-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.animation:animation-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\db25611255d1d5abacf516ad9b072a3b\transformed\animation-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.foundation:foundation-layout-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\4c774eade68a4953176655348ac95f6d\transformed\foundation-layout-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.foundation:foundation-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\7abcdc53ecef1f011b8484f7921702b8\transformed\foundation-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-unit-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\75b24f8a37bd5dfdd5e87e8d2210a3e8\transformed\ui-unit-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-geometry-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\6fbc8e5c571946cda73ab45f6bbf905d\transformed\ui-geometry-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-util-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\f6cf2e0d5cb2547ddfa9990721902c94\transformed\ui-util-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-text-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\fc100829141370ec911db6870ea088f4\transformed\ui-text-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\249fd0d998a9408cb439326895d8952b\transformed\ui-tooling-preview-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.ui:ui-graphics-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\c48ba905499e1ba3d2fda11192f445a7\transformed\ui-graphics-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3560ae3ebe7eddcdf5e1a10444b6136f\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:17:1-23:12
MERGED from [androidx.savedstate:savedstate-ktx:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7a921b8c843bbc13d3ba8ed6173795a1\transformed\savedstate-ktx-1.3.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.savedstate:savedstate-compose-android:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\50d16520087c5c0cf232e8ae267d8aad\transformed\savedstate-compose-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4ca5f03d6766baaf5071ffd734c354a0\transformed\emoji2-1.4.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-process:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\c771c85c255081600944932da00ba925\transformed\lifecycle-process-2.9.1\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\ec3debb0c557f301aabc8e7d4208d111\transformed\lifecycle-livedata-core-2.9.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime-compose-android:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\85890a2a5bdf2c7719f8c41101a737e2\transformed\lifecycle-runtime-compose-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3993c5e4f0652e0e7d9589210d919205\transformed\lifecycle-viewmodel-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate-android:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\271f3550eab8bc29f24a06e457f82177\transformed\lifecycle-viewmodel-savedstate-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.core:core-ktx:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\939522b850a7ce64f64d725b2b88bb22\transformed\core-ktx-1.16.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\42bae2e0bf615586eaed14b20f43618e\transformed\autofill-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.graphics:graphics-path:1.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\93507381dfda805c272a4a4a7d56d9bf\transformed\graphics-path-1.0.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\76f6aeeb7c72cabab8c29d18f4d3161d\transformed\core-1.16.0\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7dda5bceb85b19f16161f75eb227258f\transformed\lifecycle-runtime-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\25e14f08f242a9606d1ae7284baf04a9\transformed\lifecycle-viewmodel-ktx-2.9.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\eabf96a5766943917ceabbf79ba2060c\transformed\lifecycle-runtime-ktx\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose-android:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\c622d23e473b8bd8bfbdd9f0e6e81cd0\transformed\lifecycle-viewmodel-compose-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material:material-icons-extended-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\1df31eacb7f0eba0a496bcb5396f7db0\transformed\material-icons-extended-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material:material-icons-core-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\5c79e0a05dd3d3ea9b5bc3a265008154\transformed\material-icons-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\d61819a9c261b5407a6ee59c046430b9\transformed\ui-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity-ktx:1.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\73ec1113a9332b2c2232ce4d2d53fe9e\transformed\activity-ktx-1.10.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity-compose:1.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\b6ec644df7dac576d4b66361e232a7ad\transformed\activity-compose-1.10.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.savedstate:savedstate-android:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b3f17a82ac0ca43054f5ba6dd1a02314\transformed\savedstate-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\88a7c00ceade18bfa5a16f3aef2d60b1\transformed\runtime-saveable-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.runtime:runtime-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\5a779905f42f4be645ad5b4a35c3d76c\transformed\runtime-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\141282e457d9399fe19e2dbda1937d66\transformed\annotation-experimental-1.4.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.core:core-viewtree:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c0b5525281ff7443948eefee08cdea8d\transformed\core-viewtree-1.0.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\58460f272717bf2a0793d686f7056819\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7ff19c2728b8566beec6806d08e71845\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7fa3639fd6cd716cf0fd36823e094cd3\transformed\profileinstaller-1.4.0\AndroidManifest.xml:17:1-55:12
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\fe3938a7b35ed01afb57a303c0783491\transformed\startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\52acb3bdaeb9607d359edb195be7113e\transformed\tracing-1.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7cec9e0571fd29b99f4d82b0b999bded\transformed\core-runtime-2.2.0\AndroidManifest.xml:17:1-22:12
	package
		INJECTED from D:\AetherMCya\Aether Client\app\src\main\AndroidManifest.xml
	android:versionName
		INJECTED from D:\AetherMCya\Aether Client\app\src\main\AndroidManifest.xml
	xmlns:tools
		ADDED from D:\AetherMCya\Aether Client\app\src\main\AndroidManifest.xml:3:5-51
	android:versionCode
		INJECTED from D:\AetherMCya\Aether Client\app\src\main\AndroidManifest.xml
	xmlns:android
		ADDED from D:\AetherMCya\Aether Client\app\src\main\AndroidManifest.xml:2:11-69
uses-permission#android.permission.INTERNET
ADDED from D:\AetherMCya\Aether Client\app\src\main\AndroidManifest.xml:5:5-67
	android:name
		ADDED from D:\AetherMCya\Aether Client\app\src\main\AndroidManifest.xml:5:22-64
uses-permission#android.permission.VIBRATE
ADDED from D:\AetherMCya\Aether Client\app\src\main\AndroidManifest.xml:6:5-66
	android:name
		ADDED from D:\AetherMCya\Aether Client\app\src\main\AndroidManifest.xml:6:22-63
uses-permission#android.permission.POST_NOTIFICATIONS
ADDED from D:\AetherMCya\Aether Client\app\src\main\AndroidManifest.xml:7:5-77
	android:name
		ADDED from D:\AetherMCya\Aether Client\app\src\main\AndroidManifest.xml:7:22-74
uses-permission#android.permission.FOREGROUND_SERVICE
ADDED from D:\AetherMCya\Aether Client\app\src\main\AndroidManifest.xml:8:5-77
	android:name
		ADDED from D:\AetherMCya\Aether Client\app\src\main\AndroidManifest.xml:8:22-74
uses-permission#android.permission.FOREGROUND_SERVICE_SPECIAL_USE
ADDED from D:\AetherMCya\Aether Client\app\src\main\AndroidManifest.xml:9:5-89
	android:name
		ADDED from D:\AetherMCya\Aether Client\app\src\main\AndroidManifest.xml:9:22-86
uses-permission#android.permission.WAKE_LOCK
ADDED from D:\AetherMCya\Aether Client\app\src\main\AndroidManifest.xml:10:5-67
	android:name
		ADDED from D:\AetherMCya\Aether Client\app\src\main\AndroidManifest.xml:10:22-65
uses-permission#android.permission.REQUEST_IGNORE_BATTERY_OPTIMIZATIONS
ADDED from D:\AetherMCya\Aether Client\app\src\main\AndroidManifest.xml:11:5-94
	android:name
		ADDED from D:\AetherMCya\Aether Client\app\src\main\AndroidManifest.xml:11:22-92
uses-permission#android.permission.SYSTEM_ALERT_WINDOW
ADDED from D:\AetherMCya\Aether Client\app\src\main\AndroidManifest.xml:12:5-78
	android:name
		ADDED from D:\AetherMCya\Aether Client\app\src\main\AndroidManifest.xml:12:22-75
uses-permission#android.permission.QUERY_ALL_PACKAGES
ADDED from D:\AetherMCya\Aether Client\app\src\main\AndroidManifest.xml:13:5-15:53
	tools:ignore
		ADDED from D:\AetherMCya\Aether Client\app\src\main\AndroidManifest.xml:15:9-50
	android:name
		ADDED from D:\AetherMCya\Aether Client\app\src\main\AndroidManifest.xml:14:9-61
application
ADDED from D:\AetherMCya\Aether Client\app\src\main\AndroidManifest.xml:17:5-79:19
INJECTED from D:\AetherMCya\Aether Client\app\src\main\AndroidManifest.xml:17:5-79:19
MERGED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4ca5f03d6766baaf5071ffd734c354a0\transformed\emoji2-1.4.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4ca5f03d6766baaf5071ffd734c354a0\transformed\emoji2-1.4.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\c771c85c255081600944932da00ba925\transformed\lifecycle-process-2.9.1\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\c771c85c255081600944932da00ba925\transformed\lifecycle-process-2.9.1\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\76f6aeeb7c72cabab8c29d18f4d3161d\transformed\core-1.16.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\76f6aeeb7c72cabab8c29d18f4d3161d\transformed\core-1.16.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\58460f272717bf2a0793d686f7056819\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\58460f272717bf2a0793d686f7056819\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7fa3639fd6cd716cf0fd36823e094cd3\transformed\profileinstaller-1.4.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7fa3639fd6cd716cf0fd36823e094cd3\transformed\profileinstaller-1.4.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\fe3938a7b35ed01afb57a303c0783491\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\fe3938a7b35ed01afb57a303c0783491\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
	android:extractNativeLibs
		INJECTED from D:\AetherMCya\Aether Client\app\src\main\AndroidManifest.xml
	android:roundIcon
		ADDED from D:\AetherMCya\Aether Client\app\src\main\AndroidManifest.xml:26:9-54
	android:largeHeap
		ADDED from D:\AetherMCya\Aether Client\app\src\main\AndroidManifest.xml:22:9-33
	android:icon
		ADDED from D:\AetherMCya\Aether Client\app\src\main\AndroidManifest.xml:20:9-43
	android:networkSecurityConfig
		ADDED from D:\AetherMCya\Aether Client\app\src\main\AndroidManifest.xml:23:9-69
	android:appComponentFactory
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\76f6aeeb7c72cabab8c29d18f4d3161d\transformed\core-1.16.0\AndroidManifest.xml:28:18-86
	android:supportsRtl
		ADDED from D:\AetherMCya\Aether Client\app\src\main\AndroidManifest.xml:27:9-35
	android:label
		ADDED from D:\AetherMCya\Aether Client\app\src\main\AndroidManifest.xml:21:9-41
	android:hardwareAccelerated
		ADDED from D:\AetherMCya\Aether Client\app\src\main\AndroidManifest.xml:24:9-43
	tools:targetApi
		ADDED from D:\AetherMCya\Aether Client\app\src\main\AndroidManifest.xml:29:9-35
	android:persistent
		ADDED from D:\AetherMCya\Aether Client\app\src\main\AndroidManifest.xml:25:9-34
	android:theme
		ADDED from D:\AetherMCya\Aether Client\app\src\main\AndroidManifest.xml:28:9-50
	android:enableOnBackInvokedCallback
		ADDED from D:\AetherMCya\Aether Client\app\src\main\AndroidManifest.xml:19:9-51
	android:name
		ADDED from D:\AetherMCya\Aether Client\app\src\main\AndroidManifest.xml:18:9-38
meta-data#android.app.icon_cache_version
ADDED from D:\AetherMCya\Aether Client\app\src\main\AndroidManifest.xml:32:9-34:33
	android:value
		ADDED from D:\AetherMCya\Aether Client\app\src\main\AndroidManifest.xml:34:13-30
	android:name
		ADDED from D:\AetherMCya\Aether Client\app\src\main\AndroidManifest.xml:33:13-58
activity#com.radiantbyte.aetherclient.app.AetherActivity
ADDED from D:\AetherMCya\Aether Client\app\src\main\AndroidManifest.xml:36:9-47:20
	android:screenOrientation
		ADDED from D:\AetherMCya\Aether Client\app\src\main\AndroidManifest.xml:39:13-50
	android:exported
		ADDED from D:\AetherMCya\Aether Client\app\src\main\AndroidManifest.xml:38:13-36
	android:configChanges
		ADDED from D:\AetherMCya\Aether Client\app\src\main\AndroidManifest.xml:40:13-74
	android:name
		ADDED from D:\AetherMCya\Aether Client\app\src\main\AndroidManifest.xml:37:13-47
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from D:\AetherMCya\Aether Client\app\src\main\AndroidManifest.xml:42:13-45:29
action#android.intent.action.MAIN
ADDED from D:\AetherMCya\Aether Client\app\src\main\AndroidManifest.xml:43:17-69
	android:name
		ADDED from D:\AetherMCya\Aether Client\app\src\main\AndroidManifest.xml:43:25-66
category#android.intent.category.LAUNCHER
ADDED from D:\AetherMCya\Aether Client\app\src\main\AndroidManifest.xml:44:17-77
	android:name
		ADDED from D:\AetherMCya\Aether Client\app\src\main\AndroidManifest.xml:44:27-74
service#com.radiantbyte.aetherclient.service.AetherEngineService
ADDED from D:\AetherMCya\Aether Client\app\src\main\AndroidManifest.xml:49:9-67:19
	android:process
		ADDED from D:\AetherMCya\Aether Client\app\src\main\AndroidManifest.xml:54:13-38
	android:enabled
		ADDED from D:\AetherMCya\Aether Client\app\src\main\AndroidManifest.xml:51:13-35
	android:exported
		ADDED from D:\AetherMCya\Aether Client\app\src\main\AndroidManifest.xml:52:13-37
	android:foregroundServiceType
		ADDED from D:\AetherMCya\Aether Client\app\src\main\AndroidManifest.xml:53:13-55
	android:name
		ADDED from D:\AetherMCya\Aether Client\app\src\main\AndroidManifest.xml:50:13-56
property#android.app.PROPERTY_SPECIAL_USE_FGS_SUBTYPE
ADDED from D:\AetherMCya\Aether Client\app\src\main\AndroidManifest.xml:56:13-58:49
	android:value
		ADDED from D:\AetherMCya\Aether Client\app\src\main\AndroidManifest.xml:58:17-46
	android:name
		ADDED from D:\AetherMCya\Aether Client\app\src\main\AndroidManifest.xml:57:17-76
intent-filter#action:name:com.radiantbyte.aetherclient.engine.start+action:name:com.radiantbyte.aetherclient.engine.stop+category:name:android.intent.category.DEFAULT
ADDED from D:\AetherMCya\Aether Client\app\src\main\AndroidManifest.xml:60:13-65:29
action#com.radiantbyte.aetherclient.engine.start
ADDED from D:\AetherMCya\Aether Client\app\src\main\AndroidManifest.xml:61:17-84
	android:name
		ADDED from D:\AetherMCya\Aether Client\app\src\main\AndroidManifest.xml:61:25-81
action#com.radiantbyte.aetherclient.engine.stop
ADDED from D:\AetherMCya\Aether Client\app\src\main\AndroidManifest.xml:62:17-83
	android:name
		ADDED from D:\AetherMCya\Aether Client\app\src\main\AndroidManifest.xml:62:25-80
category#android.intent.category.DEFAULT
ADDED from D:\AetherMCya\Aether Client\app\src\main\AndroidManifest.xml:64:17-76
	android:name
		ADDED from D:\AetherMCya\Aether Client\app\src\main\AndroidManifest.xml:64:27-73
meta-data#android.support.FILE_PROVIDER_PATHS
ADDED from D:\AetherMCya\Aether Client\app\src\main\AndroidManifest.xml:74:13-76:54
	android:resource
		ADDED from D:\AetherMCya\Aether Client\app\src\main\AndroidManifest.xml:76:17-51
	android:name
		ADDED from D:\AetherMCya\Aether Client\app\src\main\AndroidManifest.xml:75:17-67
uses-sdk
INJECTED from D:\AetherMCya\Aether Client\app\src\main\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from D:\AetherMCya\Aether Client\app\src\main\AndroidManifest.xml
INJECTED from D:\AetherMCya\Aether Client\app\src\main\AndroidManifest.xml
MERGED from [androidx.navigation:navigation-runtime-android:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\6277c10fdac6864aa79dc40a9be66d06\transformed\navigation-runtime-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-runtime-android:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\6277c10fdac6864aa79dc40a9be66d06\transformed\navigation-runtime-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-common-android:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\52b211d17365fa8559962d9ffecdb379\transformed\navigation-common-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-common-android:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\52b211d17365fa8559962d9ffecdb379\transformed\navigation-common-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-compose-android:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3c48004821621421fbc139ed55d36616\transformed\navigation-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-compose-android:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3c48004821621421fbc139ed55d36616\transformed\navigation-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity:1.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\c5ff8f75be78b3ee3473720100b9266a\transformed\activity-1.10.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\c5ff8f75be78b3ee3473720100b9266a\transformed\activity-1.10.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.material3:material3-android:1.3.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\13ceaa855c6ae6b23241ae42f3bae2bf\transformed\material3-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.material3:material3-android:1.3.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\13ceaa855c6ae6b23241ae42f3bae2bf\transformed\material3-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.animation:animation-core-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\375157c509cb07e6f50b2416596e3f39\transformed\animation-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-core-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\375157c509cb07e6f50b2416596e3f39\transformed\animation-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-ripple-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\0f71de82152f5b1cd2445574a26fca79\transformed\material-ripple-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-ripple-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\0f71de82152f5b1cd2445574a26fca79\transformed\material-ripple-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\db25611255d1d5abacf516ad9b072a3b\transformed\animation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\db25611255d1d5abacf516ad9b072a3b\transformed\animation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-layout-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\4c774eade68a4953176655348ac95f6d\transformed\foundation-layout-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-layout-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\4c774eade68a4953176655348ac95f6d\transformed\foundation-layout-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\7abcdc53ecef1f011b8484f7921702b8\transformed\foundation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\7abcdc53ecef1f011b8484f7921702b8\transformed\foundation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-unit-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\75b24f8a37bd5dfdd5e87e8d2210a3e8\transformed\ui-unit-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-unit-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\75b24f8a37bd5dfdd5e87e8d2210a3e8\transformed\ui-unit-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-geometry-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\6fbc8e5c571946cda73ab45f6bbf905d\transformed\ui-geometry-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-geometry-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\6fbc8e5c571946cda73ab45f6bbf905d\transformed\ui-geometry-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-util-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\f6cf2e0d5cb2547ddfa9990721902c94\transformed\ui-util-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-util-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\f6cf2e0d5cb2547ddfa9990721902c94\transformed\ui-util-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-text-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\fc100829141370ec911db6870ea088f4\transformed\ui-text-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-text-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\fc100829141370ec911db6870ea088f4\transformed\ui-text-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\249fd0d998a9408cb439326895d8952b\transformed\ui-tooling-preview-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\249fd0d998a9408cb439326895d8952b\transformed\ui-tooling-preview-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-graphics-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\c48ba905499e1ba3d2fda11192f445a7\transformed\ui-graphics-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-graphics-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\c48ba905499e1ba3d2fda11192f445a7\transformed\ui-graphics-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3560ae3ebe7eddcdf5e1a10444b6136f\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3560ae3ebe7eddcdf5e1a10444b6136f\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.savedstate:savedstate-ktx:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7a921b8c843bbc13d3ba8ed6173795a1\transformed\savedstate-ktx-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7a921b8c843bbc13d3ba8ed6173795a1\transformed\savedstate-ktx-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.savedstate:savedstate-compose-android:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\50d16520087c5c0cf232e8ae267d8aad\transformed\savedstate-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.savedstate:savedstate-compose-android:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\50d16520087c5c0cf232e8ae267d8aad\transformed\savedstate-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4ca5f03d6766baaf5071ffd734c354a0\transformed\emoji2-1.4.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4ca5f03d6766baaf5071ffd734c354a0\transformed\emoji2-1.4.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\c771c85c255081600944932da00ba925\transformed\lifecycle-process-2.9.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\c771c85c255081600944932da00ba925\transformed\lifecycle-process-2.9.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\ec3debb0c557f301aabc8e7d4208d111\transformed\lifecycle-livedata-core-2.9.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\ec3debb0c557f301aabc8e7d4208d111\transformed\lifecycle-livedata-core-2.9.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-compose-android:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\85890a2a5bdf2c7719f8c41101a737e2\transformed\lifecycle-runtime-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-compose-android:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\85890a2a5bdf2c7719f8c41101a737e2\transformed\lifecycle-runtime-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3993c5e4f0652e0e7d9589210d919205\transformed\lifecycle-viewmodel-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3993c5e4f0652e0e7d9589210d919205\transformed\lifecycle-viewmodel-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate-android:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\271f3550eab8bc29f24a06e457f82177\transformed\lifecycle-viewmodel-savedstate-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate-android:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\271f3550eab8bc29f24a06e457f82177\transformed\lifecycle-viewmodel-savedstate-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core-ktx:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\939522b850a7ce64f64d725b2b88bb22\transformed\core-ktx-1.16.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\939522b850a7ce64f64d725b2b88bb22\transformed\core-ktx-1.16.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\42bae2e0bf615586eaed14b20f43618e\transformed\autofill-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\42bae2e0bf615586eaed14b20f43618e\transformed\autofill-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.graphics:graphics-path:1.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\93507381dfda805c272a4a4a7d56d9bf\transformed\graphics-path-1.0.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.graphics:graphics-path:1.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\93507381dfda805c272a4a4a7d56d9bf\transformed\graphics-path-1.0.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\76f6aeeb7c72cabab8c29d18f4d3161d\transformed\core-1.16.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\76f6aeeb7c72cabab8c29d18f4d3161d\transformed\core-1.16.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7dda5bceb85b19f16161f75eb227258f\transformed\lifecycle-runtime-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7dda5bceb85b19f16161f75eb227258f\transformed\lifecycle-runtime-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\25e14f08f242a9606d1ae7284baf04a9\transformed\lifecycle-viewmodel-ktx-2.9.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\25e14f08f242a9606d1ae7284baf04a9\transformed\lifecycle-viewmodel-ktx-2.9.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\eabf96a5766943917ceabbf79ba2060c\transformed\lifecycle-runtime-ktx\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\eabf96a5766943917ceabbf79ba2060c\transformed\lifecycle-runtime-ktx\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose-android:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\c622d23e473b8bd8bfbdd9f0e6e81cd0\transformed\lifecycle-viewmodel-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose-android:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\c622d23e473b8bd8bfbdd9f0e6e81cd0\transformed\lifecycle-viewmodel-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-extended-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\1df31eacb7f0eba0a496bcb5396f7db0\transformed\material-icons-extended-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-extended-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\1df31eacb7f0eba0a496bcb5396f7db0\transformed\material-icons-extended-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-core-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\5c79e0a05dd3d3ea9b5bc3a265008154\transformed\material-icons-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-core-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\5c79e0a05dd3d3ea9b5bc3a265008154\transformed\material-icons-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\d61819a9c261b5407a6ee59c046430b9\transformed\ui-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\d61819a9c261b5407a6ee59c046430b9\transformed\ui-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\73ec1113a9332b2c2232ce4d2d53fe9e\transformed\activity-ktx-1.10.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\73ec1113a9332b2c2232ce4d2d53fe9e\transformed\activity-ktx-1.10.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-compose:1.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\b6ec644df7dac576d4b66361e232a7ad\transformed\activity-compose-1.10.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-compose:1.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\b6ec644df7dac576d4b66361e232a7ad\transformed\activity-compose-1.10.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.savedstate:savedstate-android:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b3f17a82ac0ca43054f5ba6dd1a02314\transformed\savedstate-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-android:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b3f17a82ac0ca43054f5ba6dd1a02314\transformed\savedstate-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\88a7c00ceade18bfa5a16f3aef2d60b1\transformed\runtime-saveable-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\88a7c00ceade18bfa5a16f3aef2d60b1\transformed\runtime-saveable-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\5a779905f42f4be645ad5b4a35c3d76c\transformed\runtime-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\5a779905f42f4be645ad5b4a35c3d76c\transformed\runtime-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\141282e457d9399fe19e2dbda1937d66\transformed\annotation-experimental-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\141282e457d9399fe19e2dbda1937d66\transformed\annotation-experimental-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-viewtree:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c0b5525281ff7443948eefee08cdea8d\transformed\core-viewtree-1.0.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-viewtree:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c0b5525281ff7443948eefee08cdea8d\transformed\core-viewtree-1.0.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\58460f272717bf2a0793d686f7056819\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\58460f272717bf2a0793d686f7056819\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7ff19c2728b8566beec6806d08e71845\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7ff19c2728b8566beec6806d08e71845\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7fa3639fd6cd716cf0fd36823e094cd3\transformed\profileinstaller-1.4.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7fa3639fd6cd716cf0fd36823e094cd3\transformed\profileinstaller-1.4.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\fe3938a7b35ed01afb57a303c0783491\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\fe3938a7b35ed01afb57a303c0783491\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\52acb3bdaeb9607d359edb195be7113e\transformed\tracing-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\52acb3bdaeb9607d359edb195be7113e\transformed\tracing-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7cec9e0571fd29b99f4d82b0b999bded\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7cec9e0571fd29b99f4d82b0b999bded\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
	android:targetSdkVersion
		INJECTED from D:\AetherMCya\Aether Client\app\src\main\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from D:\AetherMCya\Aether Client\app\src\main\AndroidManifest.xml
provider#androidx.startup.InitializationProvider
ADDED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4ca5f03d6766baaf5071ffd734c354a0\transformed\emoji2-1.4.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\c771c85c255081600944932da00ba925\transformed\lifecycle-process-2.9.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\c771c85c255081600944932da00ba925\transformed\lifecycle-process-2.9.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7fa3639fd6cd716cf0fd36823e094cd3\transformed\profileinstaller-1.4.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7fa3639fd6cd716cf0fd36823e094cd3\transformed\profileinstaller-1.4.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\fe3938a7b35ed01afb57a303c0783491\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\fe3938a7b35ed01afb57a303c0783491\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4ca5f03d6766baaf5071ffd734c354a0\transformed\emoji2-1.4.0\AndroidManifest.xml:28:13-31
	android:authorities
		ADDED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4ca5f03d6766baaf5071ffd734c354a0\transformed\emoji2-1.4.0\AndroidManifest.xml:26:13-68
	android:exported
		ADDED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4ca5f03d6766baaf5071ffd734c354a0\transformed\emoji2-1.4.0\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4ca5f03d6766baaf5071ffd734c354a0\transformed\emoji2-1.4.0\AndroidManifest.xml:25:13-67
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4ca5f03d6766baaf5071ffd734c354a0\transformed\emoji2-1.4.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4ca5f03d6766baaf5071ffd734c354a0\transformed\emoji2-1.4.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4ca5f03d6766baaf5071ffd734c354a0\transformed\emoji2-1.4.0\AndroidManifest.xml:30:17-75
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\c771c85c255081600944932da00ba925\transformed\lifecycle-process-2.9.1\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\c771c85c255081600944932da00ba925\transformed\lifecycle-process-2.9.1\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\c771c85c255081600944932da00ba925\transformed\lifecycle-process-2.9.1\AndroidManifest.xml:30:17-78
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\76f6aeeb7c72cabab8c29d18f4d3161d\transformed\core-1.16.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\76f6aeeb7c72cabab8c29d18f4d3161d\transformed\core-1.16.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\76f6aeeb7c72cabab8c29d18f4d3161d\transformed\core-1.16.0\AndroidManifest.xml:23:9-81
permission#com.radiantbyte.aetherclient.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\76f6aeeb7c72cabab8c29d18f4d3161d\transformed\core-1.16.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\76f6aeeb7c72cabab8c29d18f4d3161d\transformed\core-1.16.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\76f6aeeb7c72cabab8c29d18f4d3161d\transformed\core-1.16.0\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\76f6aeeb7c72cabab8c29d18f4d3161d\transformed\core-1.16.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\76f6aeeb7c72cabab8c29d18f4d3161d\transformed\core-1.16.0\AndroidManifest.xml:26:22-94
uses-permission#com.radiantbyte.aetherclient.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\76f6aeeb7c72cabab8c29d18f4d3161d\transformed\core-1.16.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\76f6aeeb7c72cabab8c29d18f4d3161d\transformed\core-1.16.0\AndroidManifest.xml:26:22-94
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7fa3639fd6cd716cf0fd36823e094cd3\transformed\profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7fa3639fd6cd716cf0fd36823e094cd3\transformed\profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7fa3639fd6cd716cf0fd36823e094cd3\transformed\profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7fa3639fd6cd716cf0fd36823e094cd3\transformed\profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7fa3639fd6cd716cf0fd36823e094cd3\transformed\profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7fa3639fd6cd716cf0fd36823e094cd3\transformed\profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7fa3639fd6cd716cf0fd36823e094cd3\transformed\profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7fa3639fd6cd716cf0fd36823e094cd3\transformed\profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7fa3639fd6cd716cf0fd36823e094cd3\transformed\profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7fa3639fd6cd716cf0fd36823e094cd3\transformed\profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7fa3639fd6cd716cf0fd36823e094cd3\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7fa3639fd6cd716cf0fd36823e094cd3\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7fa3639fd6cd716cf0fd36823e094cd3\transformed\profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7fa3639fd6cd716cf0fd36823e094cd3\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7fa3639fd6cd716cf0fd36823e094cd3\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7fa3639fd6cd716cf0fd36823e094cd3\transformed\profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7fa3639fd6cd716cf0fd36823e094cd3\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7fa3639fd6cd716cf0fd36823e094cd3\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7fa3639fd6cd716cf0fd36823e094cd3\transformed\profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7fa3639fd6cd716cf0fd36823e094cd3\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7fa3639fd6cd716cf0fd36823e094cd3\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
