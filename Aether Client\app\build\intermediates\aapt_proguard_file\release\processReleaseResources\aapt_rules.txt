-keep class androidx.core.app.CoreComponentFactory { <init>(); }
-keep class androidx.core.content.FileProvider { <init>(); }
-keep class androidx.profileinstaller.ProfileInstallReceiver { <init>(); }
-keep class androidx.startup.InitializationProvider { <init>(); }
-keep class com.radiantbyte.aetherclient.app.AetherActivity { <init>(); }
-keep class com.radiantbyte.aetherclient.app.AetherApp { <init>(); }
-keep class com.radiantbyte.aetherclient.service.AetherEngineService { <init>(); }
