package com.radiantbyte.aetherproxy.definition

import com.radiantbyte.aetherproxy.util.RuntimeBlockStates
import org.cloudburstmc.nbt.NbtMap
import org.cloudburstmc.nbt.NbtType
import org.cloudburstmc.nbt.NbtUtils
import org.cloudburstmc.protocol.bedrock.data.definitions.BlockDefinition
import org.cloudburstmc.protocol.bedrock.data.definitions.ItemDefinition
import org.cloudburstmc.protocol.common.DefinitionRegistry
import org.cloudburstmc.protocol.common.NamedDefinition
import org.cloudburstmc.protocol.common.SimpleDefinitionRegistry


object Definitions {

    val legacyIdMap: MutableMap<Int, String> = HashMap()

    var itemDefinitions: DefinitionRegistry<ItemDefinition> = SimpleDefinitionRegistry.builder<ItemDefinition>()
        .build()

    var blockDefinition: DefinitionRegistry<BlockDefinition> = UnknownBlockDefinitionRegistry()

    var blockDefinitionHashed: DefinitionRegistry<BlockDefinition> = blockDefinition

    var cameraDefinitions: DefinitionRegistry<NamedDefinition> = SimpleDefinitionRegistry.builder<NamedDefinition>()
        .build()

    fun loadBlockPalette() {
        Definitions::class.java.getResourceAsStream("block_palette")?.use {
            NbtUtils.createGZIPReader(it).use { nbtInputStream ->
                val tag = nbtInputStream.readTag()
                if (tag is NbtMap) {
                    val blockList = tag.getList("blocks", NbtType.COMPOUND)
                    blockDefinition = NbtBlockDefinitionRegistry(blockList, false)
                    blockDefinitionHashed = NbtBlockDefinitionRegistry(blockList, true)

                    // Initialize RuntimeBlockStates with the loaded block definitions
                    try {
                        val definitions = mutableListOf<BlockDefinition>()
                        // Extract block definitions from the registry
                        for (i in 0 until 10000) { // Reasonable upper limit
                            val definition = blockDefinition.getDefinition(i)
                            if (definition != null) {
                                definitions.add(definition)
                            }
                        }
                        RuntimeBlockStates.initialize(definitions)
                        println("Definitions: Initialized RuntimeBlockStates with ${definitions.size} block definitions")
                    } catch (e: Exception) {
                        println("Definitions: Error initializing RuntimeBlockStates: ${e.message}")
                        e.printStackTrace()
                    }
                }
            }
        }
    }

}
