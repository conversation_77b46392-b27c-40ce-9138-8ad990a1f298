<EMAIL>/com.radiantbyte.aetherproxy.bedrock.world.Level,com.radiantbyte.aetherproxy.config.BoolValue-com.radiantbyte.aetherproxy.config.FloatValue+com.radiantbyte.aetherproxy.config.IntValue0com.radiantbyte.aetherproxy.config.IntRangeValue,com.radiantbyte.aetherproxy.config.ListValue=com.radiantbyte.aetherproxy.definition.CameraPresetDefinitionAcom.radiantbyte.aetherproxy.definition.NbtBlockDefinitionRegistryTcom.radiantbyte.aetherproxy.definition.NbtBlockDefinitionRegistry.NbtBlockDefinitionEcom.radiantbyte.aetherproxy.definition.UnknownBlockDefinitionRegistryWcom.radiantbyte.aetherproxy.definition.UnknownBlockDefinitionRegistry.UnknownDefinition1com.radiantbyte.aetherproxy.event.DisconnectEvent-com.radiantbyte.aetherproxy.event.PacketEvent)com.radiantbyte.aetherproxy.module.Module1com.radiantbyte.aetherproxy.module.ModuleCategory0com.radiantbyte.aetherproxy.module.ModuleManager5com.radiantbyte.aetherproxy.module.base.AbilityModule=com.radiantbyte.aetherproxy.module.combat.AntiKnockbackModuleOcom.radiantbyte.aetherproxy.module.combat.AntiKnockbackModule.AntiKnockbackMode6com.radiantbyte.aetherproxy.module.combat.HitboxModule8com.radiantbyte.aetherproxy.module.combat.KillauraModuleEcom.radiantbyte.aetherproxy.module.combat.KillauraModule.KillauraMode6com.radiantbyte.aetherproxy.module.combat.TargetModuleAcom.radiantbyte.aetherproxy.module.combat.TargetModule.TargetModeBcom.radiantbyte.aetherproxy.module.combat.TargetModule.AntiBotMode5com.radiantbyte.aetherproxy.module.effect.HasteModule;com.radiantbyte.aetherproxy.module.effect.NightVisionModule5com.radiantbyte.aetherproxy.module.misc.AntiAFKModule4com.radiantbyte.aetherproxy.module.misc.DesyncModule:com.radiantbyte.aetherproxy.module.misc.ShowPositionModule:<EMAIL>:com.radiantbyte.aetherproxy.module.visual.FreeCameraModule                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                     