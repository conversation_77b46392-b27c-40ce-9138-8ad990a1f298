<?xml version="1.0" encoding="UTF-8"?>
<incidents format="6" by="lint 8.11.1" type="conditional_incidents">

    <incident
        id="QueryPermissionsNeeded"
        severity="warning"
        message="As of Android 11, this method no longer returns information about all apps; see https://g.co/dev/packagevisibility for details">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/radiantbyte/aetherclient/router/main/HomePage.kt"
            line="509"
            column="44"
            startOffset="18960"
            endLine="509"
            endColumn="68"
            endOffset="18984"/>
        <map>
            <entry
                name="queryAll"
                boolean="true"/>
        </map>
    </incident>

    <incident
        id="ConfigurationScreenWidthHeight"
        severity="warning"
        message="Using Configuration.screenHeightDp instead of LocalWindowInfo.current.containerSize">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/radiantbyte/aetherclient/router/main/AccountPage.kt"
            line="264"
            column="57"
            startOffset="12974"
            endLine="264"
            endColumn="85"
            endOffset="13002"/>
    </incident>

    <incident
        id="ConfigurationScreenWidthHeight"
        severity="warning"
        message="Using Configuration.screenWidthDp instead of LocalWindowInfo.current.containerSize">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/radiantbyte/aetherclient/router/main/AccountPage.kt"
            line="264"
            column="27"
            startOffset="12944"
            endLine="264"
            endColumn="54"
            endOffset="12971"/>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; `SDK_INT` is always >= 34">
        <fix-replace
            description="Delete @RequiresApi"
            replacement=""
            priority="0">
            <range
                file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/radiantbyte/aetherclient/service/AetherEngine.kt"
                startOffset="2446"
                endOffset="2496"/>
        </fix-replace>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/radiantbyte/aetherclient/service/AetherEngine.kt"
            line="58"
            column="5"
            startOffset="2446"
            endLine="58"
            endColumn="55"
            endOffset="2496"/>
        <map>
            <condition minGE="34-∞"/>
        </map>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; `SDK_INT` is always >= 34">
        <fix-replace
            description="Delete @RequiresApi"
            replacement=""
            priority="0">
            <range
                file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/radiantbyte/aetherclient/service/AetherEngine.kt"
                startOffset="2877"
                endOffset="2927"/>
        </fix-replace>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/radiantbyte/aetherclient/service/AetherEngine.kt"
            line="75"
            column="5"
            startOffset="2877"
            endLine="75"
            endColumn="55"
            endOffset="2927"/>
        <map>
            <condition minGE="34-∞"/>
        </map>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; `SDK_INT` is always >= 33">
        <fix-replace
            description="Delete tools:targetApi"
            replacement=""
            priority="0">
            <range
                file="${:app*debug*MAIN*sourceProvider*0*manifest*0}"
                startOffset="1428"
                endOffset="1454"/>
        </fix-replace>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*manifest*0}"
            line="29"
            column="9"
            startOffset="1428"
            endLine="29"
            endColumn="35"
            endOffset="1454"/>
        <map>
            <condition minGE="33-∞"/>
        </map>
    </incident>

</incidents>
