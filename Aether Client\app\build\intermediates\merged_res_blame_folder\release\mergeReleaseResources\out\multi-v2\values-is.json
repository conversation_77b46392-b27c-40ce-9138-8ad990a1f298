{"logs": [{"outputFile": "com.radiantbyte.aetherclient.app-mergeReleaseResources-46:/values-is/values-is.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\7abcdc53ecef1f011b8484f7921702b8\\transformed\\foundation-release\\res\\values-is\\values-is.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,141,228", "endColumns": "85,86,86", "endOffsets": "136,223,310"}, "to": {"startLines": "2,87,88", "startColumns": "4,4,4", "startOffsets": "105,8806,8893", "endColumns": "85,86,86", "endOffsets": "186,8888,8975"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\d61819a9c261b5407a6ee59c046430b9\\transformed\\ui-release\\res\\values-is\\values-is.xml", "from": {"startLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "192,283,364,463,562,647,727,822,911,993,1071,1154,1224,1311,1386,1461,1535,1612,1680", "endColumns": "90,80,98,98,84,79,94,88,81,77,82,69,86,74,74,73,76,67,119", "endOffsets": "278,359,458,557,642,722,817,906,988,1066,1149,1219,1306,1381,1456,1530,1607,1675,1795"}, "to": {"startLines": "10,11,12,13,14,15,16,74,75,76,77,78,79,80,81,82,84,85,86", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "908,999,1080,1179,1278,1363,1443,7727,7816,7898,7976,8059,8129,8216,8291,8366,8541,8618,8686", "endColumns": "90,80,98,98,84,79,94,88,81,77,82,69,86,74,74,73,76,67,119", "endOffsets": "994,1075,1174,1273,1358,1438,1533,7811,7893,7971,8054,8124,8211,8286,8361,8435,8613,8681,8801"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\13ceaa855c6ae6b23241ae42f3bae2bf\\transformed\\material3-release\\res\\values-is\\values-is.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,169,281,388,500,597,696,812,953,1080,1215,1305,1406,1503,1603,1718,1844,1950,2075,2199,2341,2512,2635,2751,2870,2992,3090,3188,3297,3419,3525,3633,3736,3866,4001,4109,4214,4290,4384,4477,4591,4676,4761,4870,4950,5041,5142,5243,5338,5446,5534,5639,5740,5846,5966,6046,6148", "endColumns": "113,111,106,111,96,98,115,140,126,134,89,100,96,99,114,125,105,124,123,141,170,122,115,118,121,97,97,108,121,105,107,102,129,134,107,104,75,93,92,113,84,84,108,79,90,100,100,94,107,87,104,100,105,119,79,101,95", "endOffsets": "164,276,383,495,592,691,807,948,1075,1210,1300,1401,1498,1598,1713,1839,1945,2070,2194,2336,2507,2630,2746,2865,2987,3085,3183,3292,3414,3520,3628,3731,3861,3996,4104,4209,4285,4379,4472,4586,4671,4756,4865,4945,5036,5137,5238,5333,5441,5529,5634,5735,5841,5961,6041,6143,6239"}, "to": {"startLines": "17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1538,1652,1764,1871,1983,2080,2179,2295,2436,2563,2698,2788,2889,2986,3086,3201,3327,3433,3558,3682,3824,3995,4118,4234,4353,4475,4573,4671,4780,4902,5008,5116,5219,5349,5484,5592,5697,5773,5867,5960,6074,6159,6244,6353,6433,6524,6625,6726,6821,6929,7017,7122,7223,7329,7449,7529,7631", "endColumns": "113,111,106,111,96,98,115,140,126,134,89,100,96,99,114,125,105,124,123,141,170,122,115,118,121,97,97,108,121,105,107,102,129,134,107,104,75,93,92,113,84,84,108,79,90,100,100,94,107,87,104,100,105,119,79,101,95", "endOffsets": "1647,1759,1866,1978,2075,2174,2290,2431,2558,2693,2783,2884,2981,3081,3196,3322,3428,3553,3677,3819,3990,4113,4229,4348,4470,4568,4666,4775,4897,5003,5111,5214,5344,5479,5587,5692,5768,5862,5955,6069,6154,6239,6348,6428,6519,6620,6721,6816,6924,7012,7117,7218,7324,7444,7524,7626,7722"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\76f6aeeb7c72cabab8c29d18f4d3161d\\transformed\\core-1.16.0\\res\\values-is\\values-is.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,150,257,354,454,557,661,772", "endColumns": "94,106,96,99,102,103,110,100", "endOffsets": "145,252,349,449,552,656,767,868"}, "to": {"startLines": "3,4,5,6,7,8,9,83", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "191,286,393,490,590,693,797,8440", "endColumns": "94,106,96,99,102,103,110,100", "endOffsets": "281,388,485,585,688,792,903,8536"}}]}]}