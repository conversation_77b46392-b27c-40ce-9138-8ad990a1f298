http://schemas.android.com/apk/res-auto;;${\:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_launcher_background.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_launcher.png,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/mipmap-anydpi-v26/ic_launcher_round.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/mipmap-hdpi/ic_launcher_round.webp,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/mipmap-mdpi/ic_launcher_round.webp,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/mipmap-xhdpi/ic_launcher_round.webp,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/mipmap-xxhdpi/ic_launcher_round.webp,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/mipmap-xxxhdpi/ic_launcher_round.webp,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/mipmap-hdpi/ic_launcher_foreground.webp,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/mipmap-mdpi/ic_launcher_foreground.webp,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/mipmap-xhdpi/ic_launcher_foreground.webp,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/mipmap-xxhdpi/ic_launcher_foreground.webp,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/mipmap-xxxhdpi/ic_launcher_foreground.webp,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/mipmap-anydpi-v26/ic_launcher.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/mipmap-hdpi/ic_launcher.webp,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/mipmap-mdpi/ic_launcher.webp,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/mipmap-xhdpi/ic_launcher.webp,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/mipmap-xxhdpi/ic_launcher.webp,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/mipmap-xxxhdpi/ic_launcher.webp,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/values/themes.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/xml/file_paths.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/xml/network_security_config.xml,+color:aether_neon_green,0,V4001a0440,33001a046f,;"#00FF9F";aether_primary_variant,0,V4000600d1,**********,;"#6366F1";aether_accent_variant,0,V4000a01a8,37000a01db,;"#F59E0B";aether_background_variant,0,V4000e023f,3b000e0276,;"#1E293B";aether_on_background,0,V400150374,36001503a6,;"#F8FAFC";aether_neon_purple,0,V4001b0474,34001b04a4,;"#9945FF";aether_neon_blue,0,V4001c04a9,32001c04d7,;"#14F0FF";aether_on_surface_variant,0,V4001703df,3b00170416,;"#CBD5E1";aether_warning,0,V400210560,300021058c,;"#F59E0B";aether_primary,0,V4000500a0,30000500cc,;"#3B82F6";aether_info,0,V4002305c0,2d002305e9,;"#3B82F6";aether_neon_pink,0,V4001d04dc,32001d050a,;"#FF0A78";aether_error,0,V400220591,2e002205bb,;"#EF4444";aether_secondary_variant,0,V40008013d,3a00080173,;"#06B6D4";aether_on_secondary,0,V40014033e,350014036f,;"#FFFFFF";aether_on_surface,0,V4001603ab,33001603da,;"#E2E8F0";aether_success,0,V40020052f,300020055b,;"#10B981";aether_accent,0,V400090178,2f000901a3,;"#EF4444";aether_surface_variant,0,V4001002ac,38001002e0,;"#475569";aether_secondary,0,V40007010a,3200070138,;"#10B981";app_icon_background_color,0,V400020037,3b0002006e,;"#5865F2";aether_background,0,V4000d020b,33000d023a,;"#0F172A";aether_on_primary,0,V40013030a,3300130339,;"#FFFFFF";aether_surface,0,V4000f027b,30000f02a7,;"#334155";+drawable:ic_launcher_background,1,F;ic_launcher,2,F;+mipmap:ic_launcher_round,3,F;ic_launcher_round,4,F;ic_launcher_round,5,F;ic_launcher_round,6,F;ic_launcher_round,7,F;ic_launcher_round,8,F;ic_launcher_foreground,9,F;ic_launcher_foreground,10,F;ic_launcher_foreground,11,F;ic_launcher_foreground,12,F;ic_launcher_foreground,13,F;ic_launcher,14,F;ic_launcher,15,F;ic_launcher,16,F;ic_launcher,17,F;ic_launcher,18,F;ic_launcher,19,F;+string:cancel,20,V4001405e1,**********,;"Cancel";select,20,V4002509e5,2900250a0a,;"Select";unselect,20,V400260a0f,2d00260a38,;"Unselect";select_game,20,V4001c07d0,33001c07ff,;"Select game";third_party_licenses_title,20,V40004007f,4b000400c6,;"Third-Party Licenses";about,20,V400290aa1,2700290ac4,;"About";capturing_game_packets,20,V4000d03e6,4c000d042e,;"Capturing game packets...";overlay_icon,20,V400350d84,3500350db5,;"Overlay Icon";tips,20,V40017067d,250017069e,;"Tips";notification_permission_denied,20,V4001004a9,59001004fe,;"Notification permission denied";game_settings,20,V40012057d,37001205b0,;"Game Settings";visual,20,V4002f0c54,29002f0c79,;"Visual";start_game,20,V4001a0753,31001a0780,;"Start Game";ignore_battery_optimization_denied,20,V4003c0fec,6c003c1054,;"Ignore battery optimization permission denied";check_updates_and_news,20,V4003e1087,4a003e10cd,;"View Updates & News";no_game_selected,20,V4001d0804,3d001d083d,;"No game selected";settings,20,V4002a0ac9,2d002a0af2,;"Settings";fetch_account_successfully,20,V4004311e9,**********,;"Fetch account successfully";shortcut_opacity,20,V400340d3f,4400340d7f,;"Shortcut Button Opacity";overlay_permission_denied,20,V4000f0459,4f000f04a4,;"Overlay permission denied";xbox_device_code,20,V40044123b,3d00441274,;"Xbox device code";backend_disconnected,20,V4001b0785,4a001b07cb,;"AetherRelay disconnected.";login_in,20,V4004211b4,34004211e4,;"Log in using %s";backend_introduction,20,V4000a02f0,68000a0354,;"Set up a MITM-based interception in a few simple steps.";overlay_opacity_settings,20,V400370e0f,4500370e50,;"Opacity Settings";stop,20,V4000e0433,25000e0454,;"Stop";effect,20,V40041118a,29004111af,;"Effect";login_mode_introduction,20,V4002c0b4b,b4002c0bfb,;"If you want to log in to online mode\, add an account and select it. If you want to log in to offline mode\, deselect the account.";server_port,20,V400160649,**********,;"Server port";recommended_version,20,V4000c039e,47000c03e1,;"Recommended version\: %s";overlay_border_color_description,20,V4003a0f07,6b003a0f6e,;"Change the border color of the floating button";cannot_back,20,V40040110c,7d00401185,;"A crash has occurred and you are not allowed to return. You can only restart the app.";minecraft,20,V4000b0359,44000b0399,;"Minecraft";game_selector,20,V4001e0842,37001e0875,;"Game Selector";server,20,V400280a76,2a00280a9c,;"Servers";overlay_opacity,20,V400330cfc,4200330d3a,;"Overlay Button Opacity";overlay_icon_description,20,V400360dba,5400360e0a,;"Change the floating button icon";how_do_i_switch_login_mode,20,V4002b0af7,53002b0b46,;"How do I switch login modes?";third_party_licenses_description,20,V4000500cb,aa00050171,;"AetherClient uses several open source libraries. We acknowledge and thank all contributors to these projects.";combat,20,V4002d0c00,29002d0c25,;"Combat";delete,20,V4002409bb,29002409e0,;"Delete";crash_happened,20,V4003f10d2,39003f1107,;"Crash Happened";shortcut,20,V4003d1059,2d003d1082,;"Shortcut";overlay_opacity_description,20,V400380e55,6b00380ebc,;"Adjust transparency of overlay and shortcut buttons";select_game_first,20,V4001f087a,49001f08bf,;"Please select a game first.";backend,20,V4000902ab,44000902eb,;"AetherRelay";particle,20,V400300c7e,2d00300ca7,;"Particle";overlay_border_color,20,V400390ec1,4500390f02,;"Overlay Border Color";introduction,20,V4000801d3,d7000802a6,;"AetherClient is a Minecraft Bedrock client that utilizes a MITM approach\, providing various utilities to enhance the gameplay experience without modifying the game's memory.";misc,20,V400310cac,2500310ccd,;"Misc";motion,20,V4002e0c2a,29002e0c4f,;"Motion";what_is_this,20,V40007019c,36000701ce,;"What's this?";failed_to_launch_game,20,V4002008c4,4c0020090c,;"Failed to launch the game.";refresh,20,V400451279,2b004512a0,;"Refresh";server_host_name,20,V40015060b,3d00150644,;"Server host name";backend_connected,20,V40019070e,440019074e,;"AetherRelay connected.";home,20,V400060176,**********,;"Home";confirm,20,V4001305b5,2b001305dc,;"Confirm";app_name,20,V400010010,400001004c,;"Aether";has_been_selected,20,V400270a3d,3800270a71,;"(Selected)";fetch_account_failed,20,V400230971,49002309b6,;"Fetch account failed\: %s";request_overlay_permission,20,V400110503,**********,;"We need overlay permission to display the function floating window";add_account,20,V40022093d,330022096c,;"Add Account";change_game_settings_tip,20,V4001806a3,6a00180709,;"You need to disconnect before changing game settings.";request_ignore_battery_optimization,20,V4003b0f73,78003b0fe7,;"We need to ignore battery optimization to stay connected";config,20,V400320cd2,2900320cf7,;"Config";account,20,V400210911,2b00210938,;"Account";+style:Theme.AetherClient,21,V400030038,590003008d,;Dandroid\:Theme.Material.Light.NoActionBar,;+xml:file_paths,22,F;network_security_config,23,F;