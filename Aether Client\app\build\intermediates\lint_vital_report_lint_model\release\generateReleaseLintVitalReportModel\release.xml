<variant
    name="release"
    package="com.radiantbyte.aetherclient"
    minSdkVersion="28"
    targetSdkVersion="36.0"
    shrinking="true"
    mergedManifest="build\intermediates\merged_manifest\release\processReleaseMainManifest\AndroidManifest.xml"
    manifestMergeReport="build\outputs\logs\manifest-merger-release-report.txt"
    proguardFiles="build\intermediates\default_proguard_files\global\proguard-android-optimize.txt-8.11.1;proguard-rules.pro"
    partialResultsDir="build\intermediates\lint_vital_partial_results\release\lintVitalAnalyzeRelease\out">
  <buildFeatures
      namespacing="REQUIRED"/>
  <sourceProviders>
    <sourceProvider
        manifests="src\main\AndroidManifest.xml"
        javaDirectories="src\main\java;src\release\java;src\main\kotlin;src\release\kotlin"
        resDirectories="src\main\res;src\release\res"
        assetsDirectories="src\main\assets;src\release\assets"/>
  </sourceProviders>
  <testSourceProviders>
  </testSourceProviders>
  <testFixturesSourceProviders>
  </testFixturesSourceProviders>
  <artifact
      classOutputs="build\intermediates\javac\release\compileReleaseJavaWithJavac\classes;build\tmp\kotlin-classes\release;build\kotlinToolingMetadata;build\intermediates\compile_r_class_jar\release\generateReleaseRFile\R.jar"
      type="MAIN"
      applicationId="com.radiantbyte.aetherclient"
      generatedSourceFolders="build\generated\ap_generated_sources\release\out;build\generated\source\buildConfig\release"
      desugaredMethodsFiles="C:\Users\<USER>\.gradle\caches\8.13\transforms\5aa2c574ea3f161eff9fa2f10c9fd80d\transformed\D8BackportedDesugaredMethods.txt">
  </artifact>
</variant>
