AetherClient
Copyright 2024 Radiant Byte

This product includes software developed by:

The Android Open Source Project
Copyright The Android Open Source Project
Licensed under the Apache License, Version 2.0

JetBrains Kotlin
Copyright JetBrains s.r.o.
Licensed under the Apache License, Version 2.0

CloudburstMC Libraries
Copyright 2020 NukkitX
Licensed under the Apache License, Version 2.0

Jackson JSON processor
Copyright FasterXML, LLC
Licensed under the Apache License, Version 2.0

MinecraftAuth
Copyright net.raphimc
Licensed under the Apache License, Version 2.0

Apache Log4j
Copyright Apache Software Foundation
Licensed under the Apache License, Version 2.0

Apache Commons Lang
Copyright Apache Software Foundation
Licensed under the Apache License, Version 2.0

JUnit Testing Framework
Copyright JUnit Team
Licensed under the Eclipse Public License

This product does not claim ownership of any third-party libraries.
All trademarks are property of their respective owners.
