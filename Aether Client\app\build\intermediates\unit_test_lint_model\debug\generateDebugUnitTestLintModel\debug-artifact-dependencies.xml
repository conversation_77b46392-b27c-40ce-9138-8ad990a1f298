<dependencies>
  <compile
      roots="__local_aars__:D:\AetherProject\Aether Client\app\libs\AetherProxy.jar:unspecified@jar,:@@:app::debug,androidx.navigation:navigation-common-android:2.9.1@aar,androidx.navigation:navigation-compose-android:2.9.1@aar,androidx.navigation:navigation-runtime-android:2.9.1@aar,androidx.activity:activity-ktx:1.10.1@aar,androidx.activity:activity-compose:1.10.1@aar,androidx.compose.material3:material3-android:1.3.2@aar,androidx.compose.ui:ui-util-android:1.8.3@aar,androidx.compose.ui:ui-unit-android:1.8.3@aar,androidx.compose.ui:ui-text-android:1.8.3@aar,androidx.compose.foundation:foundation-layout-android:1.8.3@aar,androidx.compose.material:material-ripple-android:1.8.3@aar,androidx.compose.foundation:foundation-android:1.8.3@aar,androidx.compose.animation:animation-core-android:1.8.3@aar,androidx.compose.animation:animation-android:1.8.3@aar,androidx.compose.ui:ui-geometry-android:1.8.3@aar,androidx.compose.ui:ui-tooling-data-android:1.8.3@aar,androidx.compose.ui:ui-tooling-preview-android:1.8.3@aar,androidx.compose.ui:ui-graphics-android:1.8.3@aar,androidx.compose.material:material-icons-extended-android:1.7.8@aar,androidx.compose.material:material-icons-core-android:1.7.8@aar,androidx.core:core-ktx:1.16.0@aar,androidx.core:core:1.16.0@aar,androidx.lifecycle:lifecycle-livedata-core:2.9.1@aar,androidx.lifecycle:lifecycle-runtime-compose-android:2.9.1@aar,androidx.lifecycle:lifecycle-runtime-android:2.9.1@aar,androidx.savedstate:savedstate-ktx:1.3.0@aar,androidx.savedstate:savedstate-android:1.3.0@aar,androidx.lifecycle:lifecycle-common-jvm:2.9.1@jar,androidx.lifecycle:lifecycle-viewmodel-android:2.9.1@aar,androidx.lifecycle:lifecycle-viewmodel-ktx:2.9.1@aar,androidx.lifecycle:lifecycle-runtime-ktx-android:2.9.1@aar,androidx.lifecycle:lifecycle-viewmodel-compose-android:2.9.1@aar,androidx.compose.ui:ui-android:1.8.3@aar,androidx.compose.ui:ui-tooling-android:1.8.3@aar,androidx.compose.ui:ui-test-manifest:1.8.3@aar,androidx.activity:activity:1.10.1@aar,androidx.lifecycle:lifecycle-viewmodel-savedstate-android:2.9.1@aar,org.jetbrains.kotlinx:kotlinx-serialization-core-jvm:1.9.0@jar,org.jetbrains.kotlinx:kotlinx-serialization-json-jvm:1.9.0@jar,androidx.compose.runtime:runtime-saveable-android:1.8.3@aar,androidx.compose.runtime:runtime-android:1.8.3@aar,org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.10.2@jar,org.jetbrains.kotlinx:kotlinx-coroutines-android:1.10.2@jar,androidx.core:core-viewtree:1.0.0@aar,androidx.versionedparcelable:versionedparcelable:1.1.1@aar,androidx.arch.core:core-common:2.2.0@jar,androidx.collection:collection-jvm:1.5.0@jar,androidx.annotation:annotation-jvm:1.9.1@jar,androidx.annotation:annotation-experimental:1.4.1@aar,org.jetbrains.kotlin:kotlin-stdlib:2.2.0@jar,junit:junit:4.13.2@jar,org.hamcrest:hamcrest-core:1.3@jar,org.jetbrains:annotations:23.0.0@jar,org.jspecify:jspecify:1.0.0@jar">
    <dependency
        name="__local_aars__:D:\AetherProject\Aether Client\app\libs\AetherProxy.jar:unspecified@jar"
        simpleName="__local_aars__:D:\AetherProject\Aether Client\app\libs\AetherProxy.jar"/>
    <dependency
        name=":@@:app::debug"
        simpleName="artifacts::app"/>
    <dependency
        name="androidx.navigation:navigation-common-android:2.9.1@aar"
        simpleName="androidx.navigation:navigation-common-android"/>
    <dependency
        name="androidx.navigation:navigation-compose-android:2.9.1@aar"
        simpleName="androidx.navigation:navigation-compose-android"/>
    <dependency
        name="androidx.navigation:navigation-runtime-android:2.9.1@aar"
        simpleName="androidx.navigation:navigation-runtime-android"/>
    <dependency
        name="androidx.activity:activity-ktx:1.10.1@aar"
        simpleName="androidx.activity:activity-ktx"/>
    <dependency
        name="androidx.activity:activity-compose:1.10.1@aar"
        simpleName="androidx.activity:activity-compose"/>
    <dependency
        name="androidx.compose.material3:material3-android:1.3.2@aar"
        simpleName="androidx.compose.material3:material3-android"/>
    <dependency
        name="androidx.compose.ui:ui-util-android:1.8.3@aar"
        simpleName="androidx.compose.ui:ui-util-android"/>
    <dependency
        name="androidx.compose.ui:ui-unit-android:1.8.3@aar"
        simpleName="androidx.compose.ui:ui-unit-android"/>
    <dependency
        name="androidx.compose.ui:ui-text-android:1.8.3@aar"
        simpleName="androidx.compose.ui:ui-text-android"/>
    <dependency
        name="androidx.compose.foundation:foundation-layout-android:1.8.3@aar"
        simpleName="androidx.compose.foundation:foundation-layout-android"/>
    <dependency
        name="androidx.compose.material:material-ripple-android:1.8.3@aar"
        simpleName="androidx.compose.material:material-ripple-android"/>
    <dependency
        name="androidx.compose.foundation:foundation-android:1.8.3@aar"
        simpleName="androidx.compose.foundation:foundation-android"/>
    <dependency
        name="androidx.compose.animation:animation-core-android:1.8.3@aar"
        simpleName="androidx.compose.animation:animation-core-android"/>
    <dependency
        name="androidx.compose.animation:animation-android:1.8.3@aar"
        simpleName="androidx.compose.animation:animation-android"/>
    <dependency
        name="androidx.compose.ui:ui-geometry-android:1.8.3@aar"
        simpleName="androidx.compose.ui:ui-geometry-android"/>
    <dependency
        name="androidx.compose.ui:ui-tooling-data-android:1.8.3@aar"
        simpleName="androidx.compose.ui:ui-tooling-data-android"/>
    <dependency
        name="androidx.compose.ui:ui-tooling-preview-android:1.8.3@aar"
        simpleName="androidx.compose.ui:ui-tooling-preview-android"/>
    <dependency
        name="androidx.compose.ui:ui-graphics-android:1.8.3@aar"
        simpleName="androidx.compose.ui:ui-graphics-android"/>
    <dependency
        name="androidx.compose.material:material-icons-extended-android:1.7.8@aar"
        simpleName="androidx.compose.material:material-icons-extended-android"/>
    <dependency
        name="androidx.compose.material:material-icons-core-android:1.7.8@aar"
        simpleName="androidx.compose.material:material-icons-core-android"/>
    <dependency
        name="androidx.core:core-ktx:1.16.0@aar"
        simpleName="androidx.core:core-ktx"/>
    <dependency
        name="androidx.core:core:1.16.0@aar"
        simpleName="androidx.core:core"/>
    <dependency
        name="androidx.lifecycle:lifecycle-livedata-core:2.9.1@aar"
        simpleName="androidx.lifecycle:lifecycle-livedata-core"/>
    <dependency
        name="androidx.lifecycle:lifecycle-runtime-compose-android:2.9.1@aar"
        simpleName="androidx.lifecycle:lifecycle-runtime-compose-android"/>
    <dependency
        name="androidx.lifecycle:lifecycle-runtime-android:2.9.1@aar"
        simpleName="androidx.lifecycle:lifecycle-runtime-android"/>
    <dependency
        name="androidx.savedstate:savedstate-ktx:1.3.0@aar"
        simpleName="androidx.savedstate:savedstate-ktx"/>
    <dependency
        name="androidx.savedstate:savedstate-android:1.3.0@aar"
        simpleName="androidx.savedstate:savedstate-android"/>
    <dependency
        name="androidx.lifecycle:lifecycle-common-jvm:2.9.1@jar"
        simpleName="androidx.lifecycle:lifecycle-common-jvm"/>
    <dependency
        name="androidx.lifecycle:lifecycle-viewmodel-android:2.9.1@aar"
        simpleName="androidx.lifecycle:lifecycle-viewmodel-android"/>
    <dependency
        name="androidx.lifecycle:lifecycle-viewmodel-ktx:2.9.1@aar"
        simpleName="androidx.lifecycle:lifecycle-viewmodel-ktx"/>
    <dependency
        name="androidx.lifecycle:lifecycle-runtime-ktx-android:2.9.1@aar"
        simpleName="androidx.lifecycle:lifecycle-runtime-ktx-android"/>
    <dependency
        name="androidx.lifecycle:lifecycle-viewmodel-compose-android:2.9.1@aar"
        simpleName="androidx.lifecycle:lifecycle-viewmodel-compose-android"/>
    <dependency
        name="androidx.compose.ui:ui-android:1.8.3@aar"
        simpleName="androidx.compose.ui:ui-android"/>
    <dependency
        name="androidx.compose.ui:ui-tooling-android:1.8.3@aar"
        simpleName="androidx.compose.ui:ui-tooling-android"/>
    <dependency
        name="androidx.compose.ui:ui-test-manifest:1.8.3@aar"
        simpleName="androidx.compose.ui:ui-test-manifest"/>
    <dependency
        name="androidx.activity:activity:1.10.1@aar"
        simpleName="androidx.activity:activity"/>
    <dependency
        name="androidx.lifecycle:lifecycle-viewmodel-savedstate-android:2.9.1@aar"
        simpleName="androidx.lifecycle:lifecycle-viewmodel-savedstate-android"/>
    <dependency
        name="org.jetbrains.kotlinx:kotlinx-serialization-core-jvm:1.9.0@jar"
        simpleName="org.jetbrains.kotlinx:kotlinx-serialization-core-jvm"/>
    <dependency
        name="org.jetbrains.kotlinx:kotlinx-serialization-json-jvm:1.9.0@jar"
        simpleName="org.jetbrains.kotlinx:kotlinx-serialization-json-jvm"/>
    <dependency
        name="androidx.compose.runtime:runtime-saveable-android:1.8.3@aar"
        simpleName="androidx.compose.runtime:runtime-saveable-android"/>
    <dependency
        name="androidx.compose.runtime:runtime-android:1.8.3@aar"
        simpleName="androidx.compose.runtime:runtime-android"/>
    <dependency
        name="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.10.2@jar"
        simpleName="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm"/>
    <dependency
        name="org.jetbrains.kotlinx:kotlinx-coroutines-android:1.10.2@jar"
        simpleName="org.jetbrains.kotlinx:kotlinx-coroutines-android"/>
    <dependency
        name="androidx.core:core-viewtree:1.0.0@aar"
        simpleName="androidx.core:core-viewtree"/>
    <dependency
        name="androidx.versionedparcelable:versionedparcelable:1.1.1@aar"
        simpleName="androidx.versionedparcelable:versionedparcelable"/>
    <dependency
        name="androidx.arch.core:core-common:2.2.0@jar"
        simpleName="androidx.arch.core:core-common"/>
    <dependency
        name="androidx.collection:collection-jvm:1.5.0@jar"
        simpleName="androidx.collection:collection-jvm"/>
    <dependency
        name="androidx.annotation:annotation-jvm:1.9.1@jar"
        simpleName="androidx.annotation:annotation-jvm"/>
    <dependency
        name="androidx.annotation:annotation-experimental:1.4.1@aar"
        simpleName="androidx.annotation:annotation-experimental"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-stdlib:2.2.0@jar"
        simpleName="org.jetbrains.kotlin:kotlin-stdlib"/>
    <dependency
        name="junit:junit:4.13.2@jar"
        simpleName="junit:junit"/>
    <dependency
        name="org.hamcrest:hamcrest-core:1.3@jar"
        simpleName="org.hamcrest:hamcrest-core"/>
    <dependency
        name="org.jetbrains:annotations:23.0.0@jar"
        simpleName="org.jetbrains:annotations"/>
    <dependency
        name="org.jspecify:jspecify:1.0.0@jar"
        simpleName="org.jspecify:jspecify"/>
  </compile>
  <package
      roots="__local_aars__:D:\AetherProject\Aether Client\app\libs\AetherProxy.jar:unspecified@jar,junit:junit:4.13.2@jar,androidx.navigation:navigation-runtime-android:2.9.1@aar,androidx.navigation:navigation-common-android:2.9.1@aar,androidx.navigation:navigation-compose-android:2.9.1@aar,androidx.compose.material3:material3-android:1.3.2@aar,androidx.compose.material:material-android:1.8.3@aar,androidx.compose.material:material-ripple-android:1.8.3@aar,androidx.compose.foundation:foundation-android:1.8.3@aar,androidx.compose.foundation:foundation-layout-android:1.8.3@aar,androidx.compose.animation:animation-core-android:1.8.3@aar,androidx.compose.animation:animation-android:1.8.3@aar,androidx.compose.ui:ui-tooling-data-android:1.8.3@aar,androidx.compose.ui:ui-unit-android:1.8.3@aar,androidx.compose.ui:ui-geometry-android:1.8.3@aar,androidx.compose.ui:ui-util-android:1.8.3@aar,androidx.compose.ui:ui-text-android:1.8.3@aar,androidx.compose.ui:ui-tooling-preview-android:1.8.3@aar,androidx.compose.ui:ui-tooling-android:1.8.3@aar,androidx.compose.ui:ui-graphics-android:1.8.3@aar,androidx.customview:customview-poolingcontainer:1.0.0@aar,androidx.savedstate:savedstate-ktx:1.3.0@aar,androidx.savedstate:savedstate-compose-android:1.3.0@aar,androidx.emoji2:emoji2:1.4.0@aar,androidx.lifecycle:lifecycle-process:2.9.1@aar,androidx.lifecycle:lifecycle-livedata-core:2.9.1@aar,androidx.lifecycle:lifecycle-runtime-compose-android:2.9.1@aar,androidx.lifecycle:lifecycle-common-java8:2.9.1@jar,androidx.lifecycle:lifecycle-viewmodel-android:2.9.1@aar,androidx.lifecycle:lifecycle-common-jvm:2.9.1@jar,androidx.lifecycle:lifecycle-viewmodel-savedstate-android:2.9.1@aar,androidx.core:core-ktx:1.16.0@aar,androidx.autofill:autofill:1.0.0@aar,androidx.graphics:graphics-path:1.0.1@aar,androidx.core:core:1.16.0@aar,androidx.core:core:1.16.0@aar,androidx.lifecycle:lifecycle-runtime-android:2.9.1@aar,androidx.lifecycle:lifecycle-viewmodel-ktx:2.9.1@aar,androidx.lifecycle:lifecycle-runtime-ktx-android:2.9.1@aar,androidx.lifecycle:lifecycle-viewmodel-compose-android:2.9.1@aar,androidx.compose.material:material-icons-extended-android:1.7.8@aar,androidx.compose.material:material-icons-core-android:1.7.8@aar,androidx.compose.ui:ui-android:1.8.3@aar,androidx.compose.ui:ui-test-manifest:1.8.3@aar,androidx.activity:activity:1.10.1@aar,androidx.activity:activity-ktx:1.10.1@aar,androidx.activity:activity-compose:1.10.1@aar,androidx.savedstate:savedstate-android:1.3.0@aar,org.jetbrains.kotlinx:kotlinx-serialization-core-jvm:1.9.0@jar,org.jetbrains.kotlinx:kotlinx-serialization-json-jvm:1.9.0@jar,androidx.compose.runtime:runtime-saveable-android:1.8.3@aar,androidx.compose.runtime:runtime-android:1.8.3@aar,org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.10.2@jar,org.jetbrains.kotlinx:kotlinx-coroutines-android:1.10.2@jar,androidx.annotation:annotation-experimental:1.4.1@aar,androidx.core:core-viewtree:1.0.0@aar,androidx.versionedparcelable:versionedparcelable:1.1.1@aar,androidx.collection:collection-ktx:1.5.0@jar,androidx.collection:collection-jvm:1.5.0@jar,androidx.interpolator:interpolator:1.0.0@aar,androidx.profileinstaller:profileinstaller:1.4.0@aar,androidx.startup:startup-runtime:1.1.1@aar,androidx.tracing:tracing:1.2.0@aar,androidx.arch.core:core-runtime:2.2.0@aar,androidx.arch.core:core-common:2.2.0@jar,androidx.concurrent:concurrent-futures:1.1.0@jar,androidx.annotation:annotation-jvm:1.9.1@jar,org.jetbrains.kotlin:kotlin-stdlib:2.2.0@jar,org.hamcrest:hamcrest-core:1.3@jar,org.jetbrains:annotations:23.0.0@jar,org.jspecify:jspecify:1.0.0@jar,com.google.guava:listenablefuture:1.0@jar">
    <dependency
        name="__local_aars__:D:\AetherProject\Aether Client\app\libs\AetherProxy.jar:unspecified@jar"
        simpleName="__local_aars__:D:\AetherProject\Aether Client\app\libs\AetherProxy.jar"/>
    <dependency
        name="junit:junit:4.13.2@jar"
        simpleName="junit:junit"/>
    <dependency
        name="androidx.navigation:navigation-runtime-android:2.9.1@aar"
        simpleName="androidx.navigation:navigation-runtime-android"/>
    <dependency
        name="androidx.navigation:navigation-common-android:2.9.1@aar"
        simpleName="androidx.navigation:navigation-common-android"/>
    <dependency
        name="androidx.navigation:navigation-compose-android:2.9.1@aar"
        simpleName="androidx.navigation:navigation-compose-android"/>
    <dependency
        name="androidx.compose.material3:material3-android:1.3.2@aar"
        simpleName="androidx.compose.material3:material3-android"/>
    <dependency
        name="androidx.compose.material:material-android:1.8.3@aar"
        simpleName="androidx.compose.material:material-android"/>
    <dependency
        name="androidx.compose.material:material-ripple-android:1.8.3@aar"
        simpleName="androidx.compose.material:material-ripple-android"/>
    <dependency
        name="androidx.compose.foundation:foundation-android:1.8.3@aar"
        simpleName="androidx.compose.foundation:foundation-android"/>
    <dependency
        name="androidx.compose.foundation:foundation-layout-android:1.8.3@aar"
        simpleName="androidx.compose.foundation:foundation-layout-android"/>
    <dependency
        name="androidx.compose.animation:animation-core-android:1.8.3@aar"
        simpleName="androidx.compose.animation:animation-core-android"/>
    <dependency
        name="androidx.compose.animation:animation-android:1.8.3@aar"
        simpleName="androidx.compose.animation:animation-android"/>
    <dependency
        name="androidx.compose.ui:ui-tooling-data-android:1.8.3@aar"
        simpleName="androidx.compose.ui:ui-tooling-data-android"/>
    <dependency
        name="androidx.compose.ui:ui-unit-android:1.8.3@aar"
        simpleName="androidx.compose.ui:ui-unit-android"/>
    <dependency
        name="androidx.compose.ui:ui-geometry-android:1.8.3@aar"
        simpleName="androidx.compose.ui:ui-geometry-android"/>
    <dependency
        name="androidx.compose.ui:ui-util-android:1.8.3@aar"
        simpleName="androidx.compose.ui:ui-util-android"/>
    <dependency
        name="androidx.compose.ui:ui-text-android:1.8.3@aar"
        simpleName="androidx.compose.ui:ui-text-android"/>
    <dependency
        name="androidx.compose.ui:ui-tooling-preview-android:1.8.3@aar"
        simpleName="androidx.compose.ui:ui-tooling-preview-android"/>
    <dependency
        name="androidx.compose.ui:ui-tooling-android:1.8.3@aar"
        simpleName="androidx.compose.ui:ui-tooling-android"/>
    <dependency
        name="androidx.compose.ui:ui-graphics-android:1.8.3@aar"
        simpleName="androidx.compose.ui:ui-graphics-android"/>
    <dependency
        name="androidx.customview:customview-poolingcontainer:1.0.0@aar"
        simpleName="androidx.customview:customview-poolingcontainer"/>
    <dependency
        name="androidx.savedstate:savedstate-ktx:1.3.0@aar"
        simpleName="androidx.savedstate:savedstate-ktx"/>
    <dependency
        name="androidx.savedstate:savedstate-compose-android:1.3.0@aar"
        simpleName="androidx.savedstate:savedstate-compose-android"/>
    <dependency
        name="androidx.emoji2:emoji2:1.4.0@aar"
        simpleName="androidx.emoji2:emoji2"/>
    <dependency
        name="androidx.lifecycle:lifecycle-process:2.9.1@aar"
        simpleName="androidx.lifecycle:lifecycle-process"/>
    <dependency
        name="androidx.lifecycle:lifecycle-livedata-core:2.9.1@aar"
        simpleName="androidx.lifecycle:lifecycle-livedata-core"/>
    <dependency
        name="androidx.lifecycle:lifecycle-runtime-compose-android:2.9.1@aar"
        simpleName="androidx.lifecycle:lifecycle-runtime-compose-android"/>
    <dependency
        name="androidx.lifecycle:lifecycle-common-java8:2.9.1@jar"
        simpleName="androidx.lifecycle:lifecycle-common-java8"/>
    <dependency
        name="androidx.lifecycle:lifecycle-viewmodel-android:2.9.1@aar"
        simpleName="androidx.lifecycle:lifecycle-viewmodel-android"/>
    <dependency
        name="androidx.lifecycle:lifecycle-common-jvm:2.9.1@jar"
        simpleName="androidx.lifecycle:lifecycle-common-jvm"/>
    <dependency
        name="androidx.lifecycle:lifecycle-viewmodel-savedstate-android:2.9.1@aar"
        simpleName="androidx.lifecycle:lifecycle-viewmodel-savedstate-android"/>
    <dependency
        name="androidx.core:core-ktx:1.16.0@aar"
        simpleName="androidx.core:core-ktx"/>
    <dependency
        name="androidx.autofill:autofill:1.0.0@aar"
        simpleName="androidx.autofill:autofill"/>
    <dependency
        name="androidx.graphics:graphics-path:1.0.1@aar"
        simpleName="androidx.graphics:graphics-path"/>
    <dependency
        name="androidx.core:core:1.16.0@aar"
        simpleName="androidx.core:core"/>
    <dependency
        name="androidx.lifecycle:lifecycle-runtime-android:2.9.1@aar"
        simpleName="androidx.lifecycle:lifecycle-runtime-android"/>
    <dependency
        name="androidx.lifecycle:lifecycle-viewmodel-ktx:2.9.1@aar"
        simpleName="androidx.lifecycle:lifecycle-viewmodel-ktx"/>
    <dependency
        name="androidx.lifecycle:lifecycle-runtime-ktx-android:2.9.1@aar"
        simpleName="androidx.lifecycle:lifecycle-runtime-ktx-android"/>
    <dependency
        name="androidx.lifecycle:lifecycle-viewmodel-compose-android:2.9.1@aar"
        simpleName="androidx.lifecycle:lifecycle-viewmodel-compose-android"/>
    <dependency
        name="androidx.compose.material:material-icons-extended-android:1.7.8@aar"
        simpleName="androidx.compose.material:material-icons-extended-android"/>
    <dependency
        name="androidx.compose.material:material-icons-core-android:1.7.8@aar"
        simpleName="androidx.compose.material:material-icons-core-android"/>
    <dependency
        name="androidx.compose.ui:ui-android:1.8.3@aar"
        simpleName="androidx.compose.ui:ui-android"/>
    <dependency
        name="androidx.compose.ui:ui-test-manifest:1.8.3@aar"
        simpleName="androidx.compose.ui:ui-test-manifest"/>
    <dependency
        name="androidx.activity:activity:1.10.1@aar"
        simpleName="androidx.activity:activity"/>
    <dependency
        name="androidx.activity:activity-ktx:1.10.1@aar"
        simpleName="androidx.activity:activity-ktx"/>
    <dependency
        name="androidx.activity:activity-compose:1.10.1@aar"
        simpleName="androidx.activity:activity-compose"/>
    <dependency
        name="androidx.savedstate:savedstate-android:1.3.0@aar"
        simpleName="androidx.savedstate:savedstate-android"/>
    <dependency
        name="org.jetbrains.kotlinx:kotlinx-serialization-core-jvm:1.9.0@jar"
        simpleName="org.jetbrains.kotlinx:kotlinx-serialization-core-jvm"/>
    <dependency
        name="org.jetbrains.kotlinx:kotlinx-serialization-json-jvm:1.9.0@jar"
        simpleName="org.jetbrains.kotlinx:kotlinx-serialization-json-jvm"/>
    <dependency
        name="androidx.compose.runtime:runtime-saveable-android:1.8.3@aar"
        simpleName="androidx.compose.runtime:runtime-saveable-android"/>
    <dependency
        name="androidx.compose.runtime:runtime-android:1.8.3@aar"
        simpleName="androidx.compose.runtime:runtime-android"/>
    <dependency
        name="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.10.2@jar"
        simpleName="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm"/>
    <dependency
        name="org.jetbrains.kotlinx:kotlinx-coroutines-android:1.10.2@jar"
        simpleName="org.jetbrains.kotlinx:kotlinx-coroutines-android"/>
    <dependency
        name="androidx.annotation:annotation-experimental:1.4.1@aar"
        simpleName="androidx.annotation:annotation-experimental"/>
    <dependency
        name="androidx.core:core-viewtree:1.0.0@aar"
        simpleName="androidx.core:core-viewtree"/>
    <dependency
        name="androidx.versionedparcelable:versionedparcelable:1.1.1@aar"
        simpleName="androidx.versionedparcelable:versionedparcelable"/>
    <dependency
        name="androidx.collection:collection-ktx:1.5.0@jar"
        simpleName="androidx.collection:collection-ktx"/>
    <dependency
        name="androidx.collection:collection-jvm:1.5.0@jar"
        simpleName="androidx.collection:collection-jvm"/>
    <dependency
        name="androidx.interpolator:interpolator:1.0.0@aar"
        simpleName="androidx.interpolator:interpolator"/>
    <dependency
        name="androidx.profileinstaller:profileinstaller:1.4.0@aar"
        simpleName="androidx.profileinstaller:profileinstaller"/>
    <dependency
        name="androidx.startup:startup-runtime:1.1.1@aar"
        simpleName="androidx.startup:startup-runtime"/>
    <dependency
        name="androidx.tracing:tracing:1.2.0@aar"
        simpleName="androidx.tracing:tracing"/>
    <dependency
        name="androidx.arch.core:core-runtime:2.2.0@aar"
        simpleName="androidx.arch.core:core-runtime"/>
    <dependency
        name="androidx.arch.core:core-common:2.2.0@jar"
        simpleName="androidx.arch.core:core-common"/>
    <dependency
        name="androidx.concurrent:concurrent-futures:1.1.0@jar"
        simpleName="androidx.concurrent:concurrent-futures"/>
    <dependency
        name="androidx.annotation:annotation-jvm:1.9.1@jar"
        simpleName="androidx.annotation:annotation-jvm"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-stdlib:2.2.0@jar"
        simpleName="org.jetbrains.kotlin:kotlin-stdlib"/>
    <dependency
        name="org.hamcrest:hamcrest-core:1.3@jar"
        simpleName="org.hamcrest:hamcrest-core"/>
    <dependency
        name="org.jetbrains:annotations:23.0.0@jar"
        simpleName="org.jetbrains:annotations"/>
    <dependency
        name="org.jspecify:jspecify:1.0.0@jar"
        simpleName="org.jspecify:jspecify"/>
    <dependency
        name="com.google.guava:listenablefuture:1.0@jar"
        simpleName="com.google.guava:listenablefuture"/>
  </package>
</dependencies>
