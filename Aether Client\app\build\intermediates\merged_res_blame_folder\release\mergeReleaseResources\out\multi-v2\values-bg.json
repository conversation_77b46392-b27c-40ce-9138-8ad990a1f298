{"logs": [{"outputFile": "com.radiantbyte.aetherclient.app-mergeReleaseResources-46:/values-bg/values-bg.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\76f6aeeb7c72cabab8c29d18f4d3161d\\transformed\\core-1.16.0\\res\\values-bg\\values-bg.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,262,364,465,572,677,796", "endColumns": "96,109,101,100,106,104,118,100", "endOffsets": "147,257,359,460,567,672,791,892"}, "to": {"startLines": "3,4,5,6,7,8,9,83", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "193,290,400,502,603,710,815,8725", "endColumns": "96,109,101,100,106,104,118,100", "endOffsets": "285,395,497,598,705,810,929,8821"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\d61819a9c261b5407a6ee59c046430b9\\transformed\\ui-release\\res\\values-bg\\values-bg.xml", "from": {"startLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "194,297,390,493,596,680,756,847,938,1022,1106,1194,1266,1351,1428,1506,1582,1665,1734", "endColumns": "102,92,102,102,83,75,90,90,83,83,87,71,84,76,77,75,82,68,120", "endOffsets": "292,385,488,591,675,751,842,933,1017,1101,1189,1261,1346,1423,1501,1577,1660,1729,1850"}, "to": {"startLines": "10,11,12,13,14,15,16,74,75,76,77,78,79,80,81,82,84,85,86", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "934,1037,1130,1233,1336,1420,1496,7990,8081,8165,8249,8337,8409,8494,8571,8649,8826,8909,8978", "endColumns": "102,92,102,102,83,75,90,90,83,83,87,71,84,76,77,75,82,68,120", "endOffsets": "1032,1125,1228,1331,1415,1491,1582,8076,8160,8244,8332,8404,8489,8566,8644,8720,8904,8973,9094"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\7abcdc53ecef1f011b8484f7921702b8\\transformed\\foundation-release\\res\\values-bg\\values-bg.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,143,231", "endColumns": "87,87,94", "endOffsets": "138,226,321"}, "to": {"startLines": "2,87,88", "startColumns": "4,4,4", "startOffsets": "105,9099,9187", "endColumns": "87,87,94", "endOffsets": "188,9182,9277"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\13ceaa855c6ae6b23241ae42f3bae2bf\\transformed\\material3-release\\res\\values-bg\\values-bg.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,174,295,432,551,648,744,857,987,1108,1255,1339,1438,1534,1630,1743,1872,1976,2119,2262,2407,2595,2735,2862,2992,3126,3223,3320,3457,3592,3695,3800,3905,4050,4200,4308,4411,4498,4590,4685,4798,4895,4985,5094,5174,5257,5357,5459,5555,5653,5741,5848,5948,6052,6171,6251,6361", "endColumns": "118,120,136,118,96,95,112,129,120,146,83,98,95,95,112,128,103,142,142,144,187,139,126,129,133,96,96,136,134,102,104,104,144,149,107,102,86,91,94,112,96,89,108,79,82,99,101,95,97,87,106,99,103,118,79,109,96", "endOffsets": "169,290,427,546,643,739,852,982,1103,1250,1334,1433,1529,1625,1738,1867,1971,2114,2257,2402,2590,2730,2857,2987,3121,3218,3315,3452,3587,3690,3795,3900,4045,4195,4303,4406,4493,4585,4680,4793,4890,4980,5089,5169,5252,5352,5454,5550,5648,5736,5843,5943,6047,6166,6246,6356,6453"}, "to": {"startLines": "17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1587,1706,1827,1964,2083,2180,2276,2389,2519,2640,2787,2871,2970,3066,3162,3275,3404,3508,3651,3794,3939,4127,4267,4394,4524,4658,4755,4852,4989,5124,5227,5332,5437,5582,5732,5840,5943,6030,6122,6217,6330,6427,6517,6626,6706,6789,6889,6991,7087,7185,7273,7380,7480,7584,7703,7783,7893", "endColumns": "118,120,136,118,96,95,112,129,120,146,83,98,95,95,112,128,103,142,142,144,187,139,126,129,133,96,96,136,134,102,104,104,144,149,107,102,86,91,94,112,96,89,108,79,82,99,101,95,97,87,106,99,103,118,79,109,96", "endOffsets": "1701,1822,1959,2078,2175,2271,2384,2514,2635,2782,2866,2965,3061,3157,3270,3399,3503,3646,3789,3934,4122,4262,4389,4519,4653,4750,4847,4984,5119,5222,5327,5432,5577,5727,5835,5938,6025,6117,6212,6325,6422,6512,6621,6701,6784,6884,6986,7082,7180,7268,7375,7475,7579,7698,7778,7888,7985"}}]}]}