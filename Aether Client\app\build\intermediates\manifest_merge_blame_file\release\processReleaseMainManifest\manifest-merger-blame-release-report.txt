1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.radiantbyte.aetherclient"
4    android:versionCode="1"
5    android:versionName="1.0.2" >
6
7    <uses-sdk
8        android:minSdkVersion="28"
9        android:targetSdkVersion="36" />
10
11    <uses-permission android:name="android.permission.INTERNET" />
11-->D:\AetherMCya\Aether Client\app\src\main\AndroidManifest.xml:5:5-67
11-->D:\AetherMCya\Aether Client\app\src\main\AndroidManifest.xml:5:22-64
12    <uses-permission android:name="android.permission.VIBRATE" />
12-->D:\AetherMCya\Aether Client\app\src\main\AndroidManifest.xml:6:5-66
12-->D:\AetherMCya\Aether Client\app\src\main\AndroidManifest.xml:6:22-63
13    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
13-->D:\AetherMCya\Aether Client\app\src\main\AndroidManifest.xml:7:5-77
13-->D:\AetherMCya\Aether Client\app\src\main\AndroidManifest.xml:7:22-74
14    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
14-->D:\AetherMCya\Aether Client\app\src\main\AndroidManifest.xml:8:5-77
14-->D:\AetherMCya\Aether Client\app\src\main\AndroidManifest.xml:8:22-74
15    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_SPECIAL_USE" />
15-->D:\AetherMCya\Aether Client\app\src\main\AndroidManifest.xml:9:5-89
15-->D:\AetherMCya\Aether Client\app\src\main\AndroidManifest.xml:9:22-86
16    <uses-permission android:name="android.permission.WAKE_LOCK" />
16-->D:\AetherMCya\Aether Client\app\src\main\AndroidManifest.xml:10:5-67
16-->D:\AetherMCya\Aether Client\app\src\main\AndroidManifest.xml:10:22-65
17    <uses-permission android:name="android.permission.REQUEST_IGNORE_BATTERY_OPTIMIZATIONS" />
17-->D:\AetherMCya\Aether Client\app\src\main\AndroidManifest.xml:11:5-94
17-->D:\AetherMCya\Aether Client\app\src\main\AndroidManifest.xml:11:22-92
18    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
18-->D:\AetherMCya\Aether Client\app\src\main\AndroidManifest.xml:12:5-78
18-->D:\AetherMCya\Aether Client\app\src\main\AndroidManifest.xml:12:22-75
19    <uses-permission android:name="android.permission.QUERY_ALL_PACKAGES" />
19-->D:\AetherMCya\Aether Client\app\src\main\AndroidManifest.xml:13:5-15:53
19-->D:\AetherMCya\Aether Client\app\src\main\AndroidManifest.xml:14:9-61
20
21    <permission
21-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\76f6aeeb7c72cabab8c29d18f4d3161d\transformed\core-1.16.0\AndroidManifest.xml:22:5-24:47
22        android:name="com.radiantbyte.aetherclient.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
22-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\76f6aeeb7c72cabab8c29d18f4d3161d\transformed\core-1.16.0\AndroidManifest.xml:23:9-81
23        android:protectionLevel="signature" />
23-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\76f6aeeb7c72cabab8c29d18f4d3161d\transformed\core-1.16.0\AndroidManifest.xml:24:9-44
24
25    <uses-permission android:name="com.radiantbyte.aetherclient.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
25-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\76f6aeeb7c72cabab8c29d18f4d3161d\transformed\core-1.16.0\AndroidManifest.xml:26:5-97
25-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\76f6aeeb7c72cabab8c29d18f4d3161d\transformed\core-1.16.0\AndroidManifest.xml:26:22-94
26
27    <application
27-->D:\AetherMCya\Aether Client\app\src\main\AndroidManifest.xml:17:5-79:19
28        android:name="com.radiantbyte.aetherclient.app.AetherApp"
28-->D:\AetherMCya\Aether Client\app\src\main\AndroidManifest.xml:18:9-38
29        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
29-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\76f6aeeb7c72cabab8c29d18f4d3161d\transformed\core-1.16.0\AndroidManifest.xml:28:18-86
30        android:enableOnBackInvokedCallback="true"
30-->D:\AetherMCya\Aether Client\app\src\main\AndroidManifest.xml:19:9-51
31        android:extractNativeLibs="true"
32        android:hardwareAccelerated="true"
32-->D:\AetherMCya\Aether Client\app\src\main\AndroidManifest.xml:24:9-43
33        android:icon="@mipmap/ic_launcher"
33-->D:\AetherMCya\Aether Client\app\src\main\AndroidManifest.xml:20:9-43
34        android:label="@string/app_name"
34-->D:\AetherMCya\Aether Client\app\src\main\AndroidManifest.xml:21:9-41
35        android:largeHeap="true"
35-->D:\AetherMCya\Aether Client\app\src\main\AndroidManifest.xml:22:9-33
36        android:networkSecurityConfig="@xml/network_security_config"
36-->D:\AetherMCya\Aether Client\app\src\main\AndroidManifest.xml:23:9-69
37        android:persistent="true"
37-->D:\AetherMCya\Aether Client\app\src\main\AndroidManifest.xml:25:9-34
38        android:roundIcon="@mipmap/ic_launcher_round"
38-->D:\AetherMCya\Aether Client\app\src\main\AndroidManifest.xml:26:9-54
39        android:supportsRtl="true"
39-->D:\AetherMCya\Aether Client\app\src\main\AndroidManifest.xml:27:9-35
40        android:theme="@style/Theme.AetherClient" >
40-->D:\AetherMCya\Aether Client\app\src\main\AndroidManifest.xml:28:9-50
41
42        <!-- Force icon cache refresh -->
43        <meta-data
43-->D:\AetherMCya\Aether Client\app\src\main\AndroidManifest.xml:32:9-34:33
44            android:name="android.app.icon_cache_version"
44-->D:\AetherMCya\Aether Client\app\src\main\AndroidManifest.xml:33:13-58
45            android:value="1" />
45-->D:\AetherMCya\Aether Client\app\src\main\AndroidManifest.xml:34:13-30
46
47        <activity
47-->D:\AetherMCya\Aether Client\app\src\main\AndroidManifest.xml:36:9-47:20
48            android:name="com.radiantbyte.aetherclient.app.AetherActivity"
48-->D:\AetherMCya\Aether Client\app\src\main\AndroidManifest.xml:37:13-47
49            android:configChanges="orientation|screenSize|keyboardHidden"
49-->D:\AetherMCya\Aether Client\app\src\main\AndroidManifest.xml:40:13-74
50            android:exported="true"
50-->D:\AetherMCya\Aether Client\app\src\main\AndroidManifest.xml:38:13-36
51            android:screenOrientation="landscape" >
51-->D:\AetherMCya\Aether Client\app\src\main\AndroidManifest.xml:39:13-50
52            <intent-filter>
52-->D:\AetherMCya\Aether Client\app\src\main\AndroidManifest.xml:42:13-45:29
53                <action android:name="android.intent.action.MAIN" />
53-->D:\AetherMCya\Aether Client\app\src\main\AndroidManifest.xml:43:17-69
53-->D:\AetherMCya\Aether Client\app\src\main\AndroidManifest.xml:43:25-66
54
55                <category android:name="android.intent.category.LAUNCHER" />
55-->D:\AetherMCya\Aether Client\app\src\main\AndroidManifest.xml:44:17-77
55-->D:\AetherMCya\Aether Client\app\src\main\AndroidManifest.xml:44:27-74
56            </intent-filter>
57        </activity>
58
59        <service
59-->D:\AetherMCya\Aether Client\app\src\main\AndroidManifest.xml:49:9-67:19
60            android:name="com.radiantbyte.aetherclient.service.AetherEngineService"
60-->D:\AetherMCya\Aether Client\app\src\main\AndroidManifest.xml:50:13-56
61            android:enabled="true"
61-->D:\AetherMCya\Aether Client\app\src\main\AndroidManifest.xml:51:13-35
62            android:exported="false"
62-->D:\AetherMCya\Aether Client\app\src\main\AndroidManifest.xml:52:13-37
63            android:foregroundServiceType="specialUse"
63-->D:\AetherMCya\Aether Client\app\src\main\AndroidManifest.xml:53:13-55
64            android:process=":aether" >
64-->D:\AetherMCya\Aether Client\app\src\main\AndroidManifest.xml:54:13-38
65            <property
65-->D:\AetherMCya\Aether Client\app\src\main\AndroidManifest.xml:56:13-58:49
66                android:name="android.app.PROPERTY_SPECIAL_USE_FGS_SUBTYPE"
66-->D:\AetherMCya\Aether Client\app\src\main\AndroidManifest.xml:57:17-76
67                android:value="Aether Engine" />
67-->D:\AetherMCya\Aether Client\app\src\main\AndroidManifest.xml:58:17-46
68
69            <intent-filter>
69-->D:\AetherMCya\Aether Client\app\src\main\AndroidManifest.xml:60:13-65:29
70                <action android:name="com.radiantbyte.aetherclient.engine.start" />
70-->D:\AetherMCya\Aether Client\app\src\main\AndroidManifest.xml:61:17-84
70-->D:\AetherMCya\Aether Client\app\src\main\AndroidManifest.xml:61:25-81
71                <action android:name="com.radiantbyte.aetherclient.engine.stop" />
71-->D:\AetherMCya\Aether Client\app\src\main\AndroidManifest.xml:62:17-83
71-->D:\AetherMCya\Aether Client\app\src\main\AndroidManifest.xml:62:25-80
72
73                <category android:name="android.intent.category.DEFAULT" />
73-->D:\AetherMCya\Aether Client\app\src\main\AndroidManifest.xml:64:17-76
73-->D:\AetherMCya\Aether Client\app\src\main\AndroidManifest.xml:64:27-73
74            </intent-filter>
75        </service>
76
77        <provider
78            android:name="androidx.core.content.FileProvider"
78-->D:\AetherMCya\Aether Client\app\src\main\AndroidManifest.xml:70:13-62
79            android:authorities="com.radiantbyte.aetherclient.provider"
79-->D:\AetherMCya\Aether Client\app\src\main\AndroidManifest.xml:71:13-60
80            android:exported="false"
80-->D:\AetherMCya\Aether Client\app\src\main\AndroidManifest.xml:72:13-37
81            android:grantUriPermissions="true" >
81-->D:\AetherMCya\Aether Client\app\src\main\AndroidManifest.xml:73:13-47
82            <meta-data
82-->D:\AetherMCya\Aether Client\app\src\main\AndroidManifest.xml:74:13-76:54
83                android:name="android.support.FILE_PROVIDER_PATHS"
83-->D:\AetherMCya\Aether Client\app\src\main\AndroidManifest.xml:75:17-67
84                android:resource="@xml/file_paths" />
84-->D:\AetherMCya\Aether Client\app\src\main\AndroidManifest.xml:76:17-51
85        </provider>
86        <provider
86-->[androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4ca5f03d6766baaf5071ffd734c354a0\transformed\emoji2-1.4.0\AndroidManifest.xml:24:9-32:20
87            android:name="androidx.startup.InitializationProvider"
87-->[androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4ca5f03d6766baaf5071ffd734c354a0\transformed\emoji2-1.4.0\AndroidManifest.xml:25:13-67
88            android:authorities="com.radiantbyte.aetherclient.androidx-startup"
88-->[androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4ca5f03d6766baaf5071ffd734c354a0\transformed\emoji2-1.4.0\AndroidManifest.xml:26:13-68
89            android:exported="false" >
89-->[androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4ca5f03d6766baaf5071ffd734c354a0\transformed\emoji2-1.4.0\AndroidManifest.xml:27:13-37
90            <meta-data
90-->[androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4ca5f03d6766baaf5071ffd734c354a0\transformed\emoji2-1.4.0\AndroidManifest.xml:29:13-31:52
91                android:name="androidx.emoji2.text.EmojiCompatInitializer"
91-->[androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4ca5f03d6766baaf5071ffd734c354a0\transformed\emoji2-1.4.0\AndroidManifest.xml:30:17-75
92                android:value="androidx.startup" />
92-->[androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4ca5f03d6766baaf5071ffd734c354a0\transformed\emoji2-1.4.0\AndroidManifest.xml:31:17-49
93            <meta-data
93-->[androidx.lifecycle:lifecycle-process:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\c771c85c255081600944932da00ba925\transformed\lifecycle-process-2.9.1\AndroidManifest.xml:29:13-31:52
94                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
94-->[androidx.lifecycle:lifecycle-process:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\c771c85c255081600944932da00ba925\transformed\lifecycle-process-2.9.1\AndroidManifest.xml:30:17-78
95                android:value="androidx.startup" />
95-->[androidx.lifecycle:lifecycle-process:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\c771c85c255081600944932da00ba925\transformed\lifecycle-process-2.9.1\AndroidManifest.xml:31:17-49
96            <meta-data
96-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7fa3639fd6cd716cf0fd36823e094cd3\transformed\profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
97                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
97-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7fa3639fd6cd716cf0fd36823e094cd3\transformed\profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
98                android:value="androidx.startup" />
98-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7fa3639fd6cd716cf0fd36823e094cd3\transformed\profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
99        </provider>
100
101        <receiver
101-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7fa3639fd6cd716cf0fd36823e094cd3\transformed\profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
102            android:name="androidx.profileinstaller.ProfileInstallReceiver"
102-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7fa3639fd6cd716cf0fd36823e094cd3\transformed\profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
103            android:directBootAware="false"
103-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7fa3639fd6cd716cf0fd36823e094cd3\transformed\profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
104            android:enabled="true"
104-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7fa3639fd6cd716cf0fd36823e094cd3\transformed\profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
105            android:exported="true"
105-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7fa3639fd6cd716cf0fd36823e094cd3\transformed\profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
106            android:permission="android.permission.DUMP" >
106-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7fa3639fd6cd716cf0fd36823e094cd3\transformed\profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
107            <intent-filter>
107-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7fa3639fd6cd716cf0fd36823e094cd3\transformed\profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
108                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
108-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7fa3639fd6cd716cf0fd36823e094cd3\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
108-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7fa3639fd6cd716cf0fd36823e094cd3\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
109            </intent-filter>
110            <intent-filter>
110-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7fa3639fd6cd716cf0fd36823e094cd3\transformed\profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
111                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
111-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7fa3639fd6cd716cf0fd36823e094cd3\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
111-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7fa3639fd6cd716cf0fd36823e094cd3\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
112            </intent-filter>
113            <intent-filter>
113-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7fa3639fd6cd716cf0fd36823e094cd3\transformed\profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
114                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
114-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7fa3639fd6cd716cf0fd36823e094cd3\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
114-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7fa3639fd6cd716cf0fd36823e094cd3\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
115            </intent-filter>
116            <intent-filter>
116-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7fa3639fd6cd716cf0fd36823e094cd3\transformed\profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
117                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
117-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7fa3639fd6cd716cf0fd36823e094cd3\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
117-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7fa3639fd6cd716cf0fd36823e094cd3\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
118            </intent-filter>
119        </receiver>
120    </application>
121
122</manifest>
