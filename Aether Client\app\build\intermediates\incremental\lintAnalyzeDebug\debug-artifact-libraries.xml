<libraries>
  <library
      name="__local_aars__:D:\AetherProject\Aether Client\app\libs\AetherProxy.jar:unspecified@jar"
      jars="D:\AetherProject\Aether Client\app\libs\AetherProxy.jar"
      resolved="__local_aars__:D:\AetherProject\Aether Client\app\libs\AetherProxy.jar:unspecified"/>
  <library
      name="androidx.navigation:navigation-common-android:2.9.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\52b211d17365fa8559962d9ffecdb379\transformed\navigation-common-release\jars\classes.jar"
      resolved="androidx.navigation:navigation-common-android:2.9.1"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\52b211d17365fa8559962d9ffecdb379\transformed\navigation-common-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.navigation:navigation-compose-android:2.9.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\3c48004821621421fbc139ed55d36616\transformed\navigation-compose-release\jars\classes.jar"
      resolved="androidx.navigation:navigation-compose-android:2.9.1"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\3c48004821621421fbc139ed55d36616\transformed\navigation-compose-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.navigation:navigation-runtime-android:2.9.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\6277c10fdac6864aa79dc40a9be66d06\transformed\navigation-runtime-release\jars\classes.jar"
      resolved="androidx.navigation:navigation-runtime-android:2.9.1"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\6277c10fdac6864aa79dc40a9be66d06\transformed\navigation-runtime-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.activity:activity-ktx:1.10.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\73ec1113a9332b2c2232ce4d2d53fe9e\transformed\activity-ktx-1.10.1\jars\classes.jar"
      resolved="androidx.activity:activity-ktx:1.10.1"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\73ec1113a9332b2c2232ce4d2d53fe9e\transformed\activity-ktx-1.10.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.activity:activity-compose:1.10.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\b6ec644df7dac576d4b66361e232a7ad\transformed\activity-compose-1.10.1\jars\classes.jar"
      resolved="androidx.activity:activity-compose:1.10.1"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\b6ec644df7dac576d4b66361e232a7ad\transformed\activity-compose-1.10.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.material3:material3-android:1.3.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\13ceaa855c6ae6b23241ae42f3bae2bf\transformed\material3-release\jars\classes.jar"
      resolved="androidx.compose.material3:material3-android:1.3.2"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\13ceaa855c6ae6b23241ae42f3bae2bf\transformed\material3-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.ui:ui-util-android:1.8.3@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\f6cf2e0d5cb2547ddfa9990721902c94\transformed\ui-util-release\jars\classes.jar"
      resolved="androidx.compose.ui:ui-util-android:1.8.3"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\f6cf2e0d5cb2547ddfa9990721902c94\transformed\ui-util-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.ui:ui-unit-android:1.8.3@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\75b24f8a37bd5dfdd5e87e8d2210a3e8\transformed\ui-unit-release\jars\classes.jar"
      resolved="androidx.compose.ui:ui-unit-android:1.8.3"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\75b24f8a37bd5dfdd5e87e8d2210a3e8\transformed\ui-unit-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.ui:ui-text-android:1.8.3@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\fc100829141370ec911db6870ea088f4\transformed\ui-text-release\jars\classes.jar"
      resolved="androidx.compose.ui:ui-text-android:1.8.3"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\fc100829141370ec911db6870ea088f4\transformed\ui-text-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.foundation:foundation-layout-android:1.8.3@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\4c774eade68a4953176655348ac95f6d\transformed\foundation-layout-release\jars\classes.jar"
      resolved="androidx.compose.foundation:foundation-layout-android:1.8.3"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\4c774eade68a4953176655348ac95f6d\transformed\foundation-layout-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.material:material-ripple-android:1.8.3@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\0f71de82152f5b1cd2445574a26fca79\transformed\material-ripple-release\jars\classes.jar"
      resolved="androidx.compose.material:material-ripple-android:1.8.3"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\0f71de82152f5b1cd2445574a26fca79\transformed\material-ripple-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.foundation:foundation-android:1.8.3@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\7abcdc53ecef1f011b8484f7921702b8\transformed\foundation-release\jars\classes.jar"
      resolved="androidx.compose.foundation:foundation-android:1.8.3"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\7abcdc53ecef1f011b8484f7921702b8\transformed\foundation-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.animation:animation-core-android:1.8.3@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\375157c509cb07e6f50b2416596e3f39\transformed\animation-core-release\jars\classes.jar"
      resolved="androidx.compose.animation:animation-core-android:1.8.3"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\375157c509cb07e6f50b2416596e3f39\transformed\animation-core-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.animation:animation-android:1.8.3@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\db25611255d1d5abacf516ad9b072a3b\transformed\animation-release\jars\classes.jar"
      resolved="androidx.compose.animation:animation-android:1.8.3"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\db25611255d1d5abacf516ad9b072a3b\transformed\animation-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.ui:ui-geometry-android:1.8.3@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\6fbc8e5c571946cda73ab45f6bbf905d\transformed\ui-geometry-release\jars\classes.jar"
      resolved="androidx.compose.ui:ui-geometry-android:1.8.3"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\6fbc8e5c571946cda73ab45f6bbf905d\transformed\ui-geometry-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.ui:ui-tooling-data-android:1.8.3@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\18a5c4bfcab0f80cb3c11c5a9b989e4b\transformed\ui-tooling-data-release\jars\classes.jar"
      resolved="androidx.compose.ui:ui-tooling-data-android:1.8.3"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\18a5c4bfcab0f80cb3c11c5a9b989e4b\transformed\ui-tooling-data-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.ui:ui-tooling-preview-android:1.8.3@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\249fd0d998a9408cb439326895d8952b\transformed\ui-tooling-preview-release\jars\classes.jar"
      resolved="androidx.compose.ui:ui-tooling-preview-android:1.8.3"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\249fd0d998a9408cb439326895d8952b\transformed\ui-tooling-preview-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.ui:ui-graphics-android:1.8.3@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\c48ba905499e1ba3d2fda11192f445a7\transformed\ui-graphics-release\jars\classes.jar"
      resolved="androidx.compose.ui:ui-graphics-android:1.8.3"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\c48ba905499e1ba3d2fda11192f445a7\transformed\ui-graphics-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.material:material-icons-extended-android:1.7.8@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\1df31eacb7f0eba0a496bcb5396f7db0\transformed\material-icons-extended-release\jars\classes.jar"
      resolved="androidx.compose.material:material-icons-extended-android:1.7.8"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\1df31eacb7f0eba0a496bcb5396f7db0\transformed\material-icons-extended-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.material:material-icons-core-android:1.7.8@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\5c79e0a05dd3d3ea9b5bc3a265008154\transformed\material-icons-core-release\jars\classes.jar"
      resolved="androidx.compose.material:material-icons-core-android:1.7.8"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\5c79e0a05dd3d3ea9b5bc3a265008154\transformed\material-icons-core-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.core:core-ktx:1.16.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\939522b850a7ce64f64d725b2b88bb22\transformed\core-ktx-1.16.0\jars\classes.jar"
      resolved="androidx.core:core-ktx:1.16.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\939522b850a7ce64f64d725b2b88bb22\transformed\core-ktx-1.16.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.core:core:1.16.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\76f6aeeb7c72cabab8c29d18f4d3161d\transformed\core-1.16.0\jars\classes.jar"
      resolved="androidx.core:core:1.16.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\76f6aeeb7c72cabab8c29d18f4d3161d\transformed\core-1.16.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-livedata-core:2.9.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\ec3debb0c557f301aabc8e7d4208d111\transformed\lifecycle-livedata-core-2.9.1\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-livedata-core:2.9.1"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\ec3debb0c557f301aabc8e7d4208d111\transformed\lifecycle-livedata-core-2.9.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-runtime-compose-android:2.9.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\85890a2a5bdf2c7719f8c41101a737e2\transformed\lifecycle-runtime-compose-release\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-runtime-compose-android:2.9.1"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\85890a2a5bdf2c7719f8c41101a737e2\transformed\lifecycle-runtime-compose-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-runtime-android:2.9.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\7dda5bceb85b19f16161f75eb227258f\transformed\lifecycle-runtime-release\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-runtime-android:2.9.1"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\7dda5bceb85b19f16161f75eb227258f\transformed\lifecycle-runtime-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.savedstate:savedstate-ktx:1.3.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\7a921b8c843bbc13d3ba8ed6173795a1\transformed\savedstate-ktx-1.3.0\jars\classes.jar"
      resolved="androidx.savedstate:savedstate-ktx:1.3.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\7a921b8c843bbc13d3ba8ed6173795a1\transformed\savedstate-ktx-1.3.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.savedstate:savedstate-android:1.3.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\b3f17a82ac0ca43054f5ba6dd1a02314\transformed\savedstate-release\jars\classes.jar"
      resolved="androidx.savedstate:savedstate-android:1.3.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\b3f17a82ac0ca43054f5ba6dd1a02314\transformed\savedstate-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-common-jvm:2.9.1@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.lifecycle\lifecycle-common-jvm\2.9.1\ab739bccdb3541983385af169565c7c035d897e7\lifecycle-common-jvm-2.9.1.jar"
      resolved="androidx.lifecycle:lifecycle-common-jvm:2.9.1"/>
  <library
      name="androidx.lifecycle:lifecycle-viewmodel-android:2.9.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\3993c5e4f0652e0e7d9589210d919205\transformed\lifecycle-viewmodel-release\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-viewmodel-android:2.9.1"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\3993c5e4f0652e0e7d9589210d919205\transformed\lifecycle-viewmodel-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-viewmodel-ktx:2.9.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\25e14f08f242a9606d1ae7284baf04a9\transformed\lifecycle-viewmodel-ktx-2.9.1\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-viewmodel-ktx:2.9.1"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\25e14f08f242a9606d1ae7284baf04a9\transformed\lifecycle-viewmodel-ktx-2.9.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-runtime-ktx-android:2.9.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\eabf96a5766943917ceabbf79ba2060c\transformed\lifecycle-runtime-ktx\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-runtime-ktx-android:2.9.1"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\eabf96a5766943917ceabbf79ba2060c\transformed\lifecycle-runtime-ktx"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-viewmodel-compose-android:2.9.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\c622d23e473b8bd8bfbdd9f0e6e81cd0\transformed\lifecycle-viewmodel-compose-release\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-viewmodel-compose-android:2.9.1"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\c622d23e473b8bd8bfbdd9f0e6e81cd0\transformed\lifecycle-viewmodel-compose-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.ui:ui-android:1.8.3@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\d61819a9c261b5407a6ee59c046430b9\transformed\ui-release\jars\classes.jar"
      resolved="androidx.compose.ui:ui-android:1.8.3"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\d61819a9c261b5407a6ee59c046430b9\transformed\ui-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.ui:ui-tooling-android:1.8.3@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\33d38d2d1973548363362dc202c477f1\transformed\ui-tooling-release\jars\classes.jar"
      resolved="androidx.compose.ui:ui-tooling-android:1.8.3"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\33d38d2d1973548363362dc202c477f1\transformed\ui-tooling-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.ui:ui-test-manifest:1.8.3@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\c8e5fc75b6f00457e9c07cc5f7b0ed19\transformed\ui-test-manifest-1.8.3\jars\classes.jar"
      resolved="androidx.compose.ui:ui-test-manifest:1.8.3"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\c8e5fc75b6f00457e9c07cc5f7b0ed19\transformed\ui-test-manifest-1.8.3"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.activity:activity:1.10.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\c5ff8f75be78b3ee3473720100b9266a\transformed\activity-1.10.1\jars\classes.jar"
      resolved="androidx.activity:activity:1.10.1"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\c5ff8f75be78b3ee3473720100b9266a\transformed\activity-1.10.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-viewmodel-savedstate-android:2.9.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\271f3550eab8bc29f24a06e457f82177\transformed\lifecycle-viewmodel-savedstate-release\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-viewmodel-savedstate-android:2.9.1"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\271f3550eab8bc29f24a06e457f82177\transformed\lifecycle-viewmodel-savedstate-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="org.jetbrains.kotlinx:kotlinx-serialization-core-jvm:1.9.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlinx\kotlinx-serialization-core-jvm\1.9.0\91448df39c558f7c6147b8bd8db01debe16e0cc1\kotlinx-serialization-core-jvm-1.9.0.jar"
      resolved="org.jetbrains.kotlinx:kotlinx-serialization-core-jvm:1.9.0"/>
  <library
      name="org.jetbrains.kotlinx:kotlinx-serialization-json-jvm:1.9.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlinx\kotlinx-serialization-json-jvm\1.9.0\aea6f7d49fe5c458f8963ee6d4bdaf4a459ab3e7\kotlinx-serialization-json-jvm-1.9.0.jar"
      resolved="org.jetbrains.kotlinx:kotlinx-serialization-json-jvm:1.9.0"/>
  <library
      name="androidx.compose.runtime:runtime-saveable-android:1.8.3@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\88a7c00ceade18bfa5a16f3aef2d60b1\transformed\runtime-saveable-release\jars\classes.jar"
      resolved="androidx.compose.runtime:runtime-saveable-android:1.8.3"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\88a7c00ceade18bfa5a16f3aef2d60b1\transformed\runtime-saveable-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.runtime:runtime-android:1.8.3@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\5a779905f42f4be645ad5b4a35c3d76c\transformed\runtime-release\jars\classes.jar"
      resolved="androidx.compose.runtime:runtime-android:1.8.3"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\5a779905f42f4be645ad5b4a35c3d76c\transformed\runtime-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.10.2@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlinx\kotlinx-coroutines-core-jvm\1.10.2\4a9f78ef49483748e2c129f3d124b8fa249dafbf\kotlinx-coroutines-core-jvm-1.10.2.jar"
      resolved="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.10.2"/>
  <library
      name="org.jetbrains.kotlinx:kotlinx-coroutines-android:1.10.2@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlinx\kotlinx-coroutines-android\1.10.2\c8705d275d81f19e8afaf3ff9b5bf7a4b6b6c19b\kotlinx-coroutines-android-1.10.2.jar"
      resolved="org.jetbrains.kotlinx:kotlinx-coroutines-android:1.10.2"/>
  <library
      name="androidx.core:core-viewtree:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\c0b5525281ff7443948eefee08cdea8d\transformed\core-viewtree-1.0.0\jars\classes.jar"
      resolved="androidx.core:core-viewtree:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\c0b5525281ff7443948eefee08cdea8d\transformed\core-viewtree-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.versionedparcelable:versionedparcelable:1.1.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\58460f272717bf2a0793d686f7056819\transformed\versionedparcelable-1.1.1\jars\classes.jar"
      resolved="androidx.versionedparcelable:versionedparcelable:1.1.1"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\58460f272717bf2a0793d686f7056819\transformed\versionedparcelable-1.1.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.arch.core:core-common:2.2.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.arch.core\core-common\2.2.0\5e1b8b81dfd5f52c56a8d53b18ca759c19a301f3\core-common-2.2.0.jar"
      resolved="androidx.arch.core:core-common:2.2.0"/>
  <library
      name="androidx.collection:collection-jvm:1.5.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.collection\collection-jvm\1.5.0\7ba2c69414d46ebc2dd76598bdd0a75c54281a57\collection-jvm-1.5.0.jar"
      resolved="androidx.collection:collection-jvm:1.5.0"/>
  <library
      name="androidx.annotation:annotation-jvm:1.9.1@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.annotation\annotation-jvm\1.9.1\b17951747e38bf3986a24431b9ba0d039958aa5f\annotation-jvm-1.9.1.jar"
      resolved="androidx.annotation:annotation-jvm:1.9.1"/>
  <library
      name="androidx.annotation:annotation-experimental:1.4.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\141282e457d9399fe19e2dbda1937d66\transformed\annotation-experimental-1.4.1\jars\classes.jar"
      resolved="androidx.annotation:annotation-experimental:1.4.1"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\141282e457d9399fe19e2dbda1937d66\transformed\annotation-experimental-1.4.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="org.jetbrains.kotlin:kotlin-stdlib:2.2.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-stdlib\2.2.0\fdfc65fbc42fda253a26f61dac3c0aca335fae96\kotlin-stdlib-2.2.0.jar"
      resolved="org.jetbrains.kotlin:kotlin-stdlib:2.2.0"/>
  <library
      name="org.jetbrains:annotations:23.0.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains\annotations\23.0.0\8cc20c07506ec18e0834947b84a864bfc094484e\annotations-23.0.0.jar"
      resolved="org.jetbrains:annotations:23.0.0"/>
  <library
      name="org.jspecify:jspecify:1.0.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jspecify\jspecify\1.0.0\7425a601c1c7ec76645a78d22b8c6a627edee507\jspecify-1.0.0.jar"
      resolved="org.jspecify:jspecify:1.0.0"/>
  <library
      name="androidx.compose.material:material-android:1.8.3@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\9956db117c03da20ac870219d39b93e1\transformed\material-release\jars\classes.jar"
      resolved="androidx.compose.material:material-android:1.8.3"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\9956db117c03da20ac870219d39b93e1\transformed\material-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.customview:customview-poolingcontainer:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\3560ae3ebe7eddcdf5e1a10444b6136f\transformed\customview-poolingcontainer-1.0.0\jars\classes.jar"
      resolved="androidx.customview:customview-poolingcontainer:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\3560ae3ebe7eddcdf5e1a10444b6136f\transformed\customview-poolingcontainer-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.savedstate:savedstate-compose-android:1.3.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\50d16520087c5c0cf232e8ae267d8aad\transformed\savedstate-compose-release\jars\classes.jar"
      resolved="androidx.savedstate:savedstate-compose-android:1.3.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\50d16520087c5c0cf232e8ae267d8aad\transformed\savedstate-compose-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.emoji2:emoji2:1.4.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\4ca5f03d6766baaf5071ffd734c354a0\transformed\emoji2-1.4.0\jars\classes.jar;C:\Users\<USER>\.gradle\caches\8.13\transforms\4ca5f03d6766baaf5071ffd734c354a0\transformed\emoji2-1.4.0\jars\libs\repackaged.jar"
      resolved="androidx.emoji2:emoji2:1.4.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\4ca5f03d6766baaf5071ffd734c354a0\transformed\emoji2-1.4.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-process:2.9.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\c771c85c255081600944932da00ba925\transformed\lifecycle-process-2.9.1\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-process:2.9.1"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\c771c85c255081600944932da00ba925\transformed\lifecycle-process-2.9.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-common-java8:2.9.1@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.lifecycle\lifecycle-common-java8\2.9.1\86a8e6b9d0ba0c964fdc7223a38b6ce74bcb24dd\lifecycle-common-java8-2.9.1.jar"
      resolved="androidx.lifecycle:lifecycle-common-java8:2.9.1"/>
  <library
      name="androidx.autofill:autofill:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\42bae2e0bf615586eaed14b20f43618e\transformed\autofill-1.0.0\jars\classes.jar"
      resolved="androidx.autofill:autofill:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\42bae2e0bf615586eaed14b20f43618e\transformed\autofill-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.graphics:graphics-path:1.0.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\93507381dfda805c272a4a4a7d56d9bf\transformed\graphics-path-1.0.1\jars\classes.jar"
      resolved="androidx.graphics:graphics-path:1.0.1"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\93507381dfda805c272a4a4a7d56d9bf\transformed\graphics-path-1.0.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.collection:collection-ktx:1.5.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.collection\collection-ktx\1.5.0\27c78a926a16a1bf792b2285cf2834e8caae4a07\collection-ktx-1.5.0.jar"
      resolved="androidx.collection:collection-ktx:1.5.0"/>
  <library
      name="androidx.interpolator:interpolator:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\7ff19c2728b8566beec6806d08e71845\transformed\interpolator-1.0.0\jars\classes.jar"
      resolved="androidx.interpolator:interpolator:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\7ff19c2728b8566beec6806d08e71845\transformed\interpolator-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.profileinstaller:profileinstaller:1.4.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\7fa3639fd6cd716cf0fd36823e094cd3\transformed\profileinstaller-1.4.0\jars\classes.jar"
      resolved="androidx.profileinstaller:profileinstaller:1.4.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\7fa3639fd6cd716cf0fd36823e094cd3\transformed\profileinstaller-1.4.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.startup:startup-runtime:1.1.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\fe3938a7b35ed01afb57a303c0783491\transformed\startup-runtime-1.1.1\jars\classes.jar"
      resolved="androidx.startup:startup-runtime:1.1.1"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\fe3938a7b35ed01afb57a303c0783491\transformed\startup-runtime-1.1.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.tracing:tracing:1.2.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\52acb3bdaeb9607d359edb195be7113e\transformed\tracing-1.2.0\jars\classes.jar"
      resolved="androidx.tracing:tracing:1.2.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\52acb3bdaeb9607d359edb195be7113e\transformed\tracing-1.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.arch.core:core-runtime:2.2.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\7cec9e0571fd29b99f4d82b0b999bded\transformed\core-runtime-2.2.0\jars\classes.jar"
      resolved="androidx.arch.core:core-runtime:2.2.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\7cec9e0571fd29b99f4d82b0b999bded\transformed\core-runtime-2.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.concurrent:concurrent-futures:1.1.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.concurrent\concurrent-futures\1.1.0\50b7fb98350d5f42a4e49704b03278542293ba48\concurrent-futures-1.1.0.jar"
      resolved="androidx.concurrent:concurrent-futures:1.1.0"/>
  <library
      name="com.google.guava:listenablefuture:1.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.google.guava\listenablefuture\1.0\c949a840a6acbc5268d088e47b04177bf90b3cad\listenablefuture-1.0.jar"
      resolved="com.google.guava:listenablefuture:1.0"/>
</libraries>
