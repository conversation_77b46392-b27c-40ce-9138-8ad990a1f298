D:\AetherProject\Aether Client\app\src\main\java\com\radiantbyte\aetherclient\app\AetherApp.kt:49: Warning: Must call getDefaultUncaughtExceptionHandler() to get the existing handler, and call existingHandler.uncaughtException(thread, throwable) from your new handler [DefaultUncaughtExceptionDelegation]
        Thread.setDefaultUncaughtExceptionHandler(errorHandler)
        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "DefaultUncaughtExceptionDelegation":
   A default uncaught exception handler should usually call the existing
   (previously set) default uncaught exception handler. This is especially
   true on Android, which uses a default uncaught exception handler to handle
   crashes. This lint check reports calls to
   setDefaultUncaughtExceptionHandler unless we can also see a call to
   getDefaultUncaughtExceptionHandler (to get the existing handler) in the
   same module. Make sure you also call
   existingHandler.uncaughtException(thread, throwable) from your new
   handler.

D:\AetherProject\Aether Client\gradle\wrapper\gradle-wrapper.properties:3: Warning: A newer version of Gradle than 8.13 is available: 8.14.3 [AndroidGradlePluginVersion]
distributionUrl=https\://services.gradle.org/distributions/gradle-8.13-bin.zip
                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "AndroidGradlePluginVersion":
   This detector looks for usage of the Android Gradle Plugin where the
   version you are using is not the current stable release. Using older
   versions is fine, and there are cases where you deliberately want to stick
   with an older version. However, you may simply not be aware that a more
   recent version is available, and that is what this lint check helps find.

D:\AetherProject\Aether Client\gradle\libs.versions.toml:10: Warning: A newer version of androidx.lifecycle:lifecycle-runtime-ktx than 2.9.1 is available: 2.9.2 [GradleDependency]
lifecycleRuntimeKtx = "2.9.1"
                      ~~~~~~~
D:\AetherProject\Aether Client\gradle\libs.versions.toml:12: Warning: A newer version of androidx.compose:compose-bom than 2025.06.01 is available: 2025.07.00 [GradleDependency]
composeBom = "2025.06.01"
             ~~~~~~~~~~~~
D:\AetherProject\Aether Client\gradle\libs.versions.toml:14: Warning: A newer version of androidx.navigation:navigation-compose than 2.9.1 is available: 2.9.2 [GradleDependency]
navigationCompose = "2.9.1"
                    ~~~~~~~

   Explanation for issues of type "GradleDependency":
   This detector looks for usages of libraries where the version you are using
   is not the current stable release. Using older versions is fine, and there
   are cases where you deliberately want to stick with an older version.
   However, you may simply not be aware that a more recent version is
   available, and that is what this lint check helps find.

D:\AetherProject\Aether Client\gradle\libs.versions.toml:16: Warning: A newer version of org.apache.logging.log4j:log4j-bom than 2.25.0 is available: 2.25.1 [NewerVersionAvailable]
log4j = "2.25.0"
        ~~~~~~~~
D:\AetherProject\Aether Client\gradle\libs.versions.toml:27: Warning: A newer version of com.fasterxml.jackson.core:jackson-databind than 2.19.1 is available: 2.19.2 [NewerVersionAvailable]
jackson-databind = "2.19.1"
                   ~~~~~~~~

   Explanation for issues of type "NewerVersionAvailable":
   This detector checks with a central repository to see if there are newer
   versions available for the dependencies used by this project. This is
   similar to the GradleDependency check, which checks for newer versions
   available in the Android SDK tools and libraries, but this works with any
   MavenCentral dependency, and connects to the library every time, which
   makes it more flexible but also much slower.

D:\AetherProject\Aether Client\app\src\main\java\com\radiantbyte\aetherclient\router\main\AccountPage.kt:264: Warning: Using Configuration.screenHeightDp instead of LocalWindowInfo.current.containerSize [ConfigurationScreenWidthHeight from androidx.compose.ui]
        val isLandscape = configuration.screenWidthDp > configuration.screenHeightDp
                                                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\AetherProject\Aether Client\app\src\main\java\com\radiantbyte\aetherclient\router\main\AccountPage.kt:264: Warning: Using Configuration.screenWidthDp instead of LocalWindowInfo.current.containerSize [ConfigurationScreenWidthHeight from androidx.compose.ui]
        val isLandscape = configuration.screenWidthDp > configuration.screenHeightDp
                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "ConfigurationScreenWidthHeight":
   Configuration.screenWidthDp and Configuration.screenHeightDp have different
   insets behaviour depending on target SDK version, and are rounded to the
   nearest Dp. This means that using these values in composition to size a
   layout can result in issues, as these values do not accurately represent
   the actual available window size. Instead it is recommended to use
   WindowInfo.containerSize which accurately represents the window size.

   Vendor: Jetpack Compose
   Identifier: androidx.compose.ui
   Feedback: https://issuetracker.google.com/issues/new?component=612128

D:\AetherProject\Aether Client\app\src\main\AndroidManifest.xml:39: Warning: Fixed screen orientations will be ignored in most cases, starting from Android 16. Android is moving toward a model where apps are expected to adapt to various orientations, display sizes, and aspect ratios. [DiscouragedApi]
            android:screenOrientation="landscape"
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "DiscouragedApi":
   Discouraged APIs are allowed and are not deprecated, but they may be unfit
   for common use (e.g. due to slow performance or subtle behavior).

D:\AetherProject\Aether Client\app\src\main\java\com\radiantbyte\aetherclient\service\AetherEngine.kt:68: Warning: Provide a timeout when requesting a wakelock with PowerManager.Wakelock.acquire(long timeout). This will ensure the OS will cleanup any wakelocks that last longer than you intend, and will save your user's battery. [WakelockTimeout]
                acquire()
                ~~~~~~~~~

   Explanation for issues of type "WakelockTimeout":
   Wakelocks have two acquire methods: one with a timeout, and one without.
   You should generally always use the one with a timeout. A typical timeout
   is 10 minutes. If the task takes longer than it is critical that it happens
   (i.e. can't use JobScheduler) then maybe they should consider a foreground
   service instead (which is a stronger run guarantee and lets the user know
   something long/important is happening).

D:\AetherProject\Aether Client\app\src\main\java\com\radiantbyte\aetherclient\service\AetherEngine.kt:327: Warning: Unnecessary; SDK_INT is always >= 28 [ObsoleteSdkInt]
                type = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\AetherProject\Aether Client\app\src\main\java\com\radiantbyte\aetherclient\util\MinecraftVersionDetector.kt:40: Warning: Unnecessary; SDK_INT is always >= 28 [ObsoleteSdkInt]
            val versionCode = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.P) {
                                  ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\AetherProject\Aether Client\app\src\main\res\mipmap-anydpi-v26: Warning: This folder configuration (v26) is unnecessary; minSdkVersion is 28. Merge all the resources in this folder into mipmap-anydpi. [ObsoleteSdkInt]

   Explanation for issues of type "ObsoleteSdkInt":
   This check flags version checks that are not necessary, because the
   minSdkVersion (or surrounding known API level) is already at least as high
   as the version checked for.

   Similarly, it also looks for resources in -vNN folders, such as values-v14
   where the version qualifier is less than or equal to the minSdkVersion,
   where the contents should be merged into the best folder.

D:\AetherProject\Aether Client\app\src\main\res\values\colors.xml:3: Warning: The resource R.color.app_icon_background_color appears to be unused [UnusedResources]
    <color name="app_icon_background_color">#5865F2</color>
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\AetherProject\Aether Client\app\src\main\res\values\colors.xml:6: Warning: The resource R.color.aether_primary appears to be unused [UnusedResources]
    <color name="aether_primary">#3B82F6</color>
           ~~~~~~~~~~~~~~~~~~~~~
D:\AetherProject\Aether Client\app\src\main\res\values\colors.xml:7: Warning: The resource R.color.aether_primary_variant appears to be unused [UnusedResources]
    <color name="aether_primary_variant">#6366F1</color>
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\AetherProject\Aether Client\app\src\main\res\values\colors.xml:8: Warning: The resource R.color.aether_secondary appears to be unused [UnusedResources]
    <color name="aether_secondary">#10B981</color>
           ~~~~~~~~~~~~~~~~~~~~~~~
D:\AetherProject\Aether Client\app\src\main\res\values\colors.xml:9: Warning: The resource R.color.aether_secondary_variant appears to be unused [UnusedResources]
    <color name="aether_secondary_variant">#06B6D4</color>
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\AetherProject\Aether Client\app\src\main\res\values\colors.xml:10: Warning: The resource R.color.aether_accent appears to be unused [UnusedResources]
    <color name="aether_accent">#EF4444</color>
           ~~~~~~~~~~~~~~~~~~~~
D:\AetherProject\Aether Client\app\src\main\res\values\colors.xml:11: Warning: The resource R.color.aether_accent_variant appears to be unused [UnusedResources]
    <color name="aether_accent_variant">#F59E0B</color>
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\AetherProject\Aether Client\app\src\main\res\values\colors.xml:14: Warning: The resource R.color.aether_background appears to be unused [UnusedResources]
    <color name="aether_background">#0F172A</color>
           ~~~~~~~~~~~~~~~~~~~~~~~~
D:\AetherProject\Aether Client\app\src\main\res\values\colors.xml:15: Warning: The resource R.color.aether_background_variant appears to be unused [UnusedResources]
    <color name="aether_background_variant">#1E293B</color>
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\AetherProject\Aether Client\app\src\main\res\values\colors.xml:16: Warning: The resource R.color.aether_surface appears to be unused [UnusedResources]
    <color name="aether_surface">#334155</color>
           ~~~~~~~~~~~~~~~~~~~~~
D:\AetherProject\Aether Client\app\src\main\res\values\colors.xml:17: Warning: The resource R.color.aether_surface_variant appears to be unused [UnusedResources]
    <color name="aether_surface_variant">#475569</color>
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\AetherProject\Aether Client\app\src\main\res\values\colors.xml:20: Warning: The resource R.color.aether_on_primary appears to be unused [UnusedResources]
    <color name="aether_on_primary">#FFFFFF</color>
           ~~~~~~~~~~~~~~~~~~~~~~~~
D:\AetherProject\Aether Client\app\src\main\res\values\colors.xml:21: Warning: The resource R.color.aether_on_secondary appears to be unused [UnusedResources]
    <color name="aether_on_secondary">#FFFFFF</color>
           ~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\AetherProject\Aether Client\app\src\main\res\values\colors.xml:22: Warning: The resource R.color.aether_on_background appears to be unused [UnusedResources]
    <color name="aether_on_background">#F8FAFC</color>
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\AetherProject\Aether Client\app\src\main\res\values\colors.xml:23: Warning: The resource R.color.aether_on_surface appears to be unused [UnusedResources]
    <color name="aether_on_surface">#E2E8F0</color>
           ~~~~~~~~~~~~~~~~~~~~~~~~
D:\AetherProject\Aether Client\app\src\main\res\values\colors.xml:24: Warning: The resource R.color.aether_on_surface_variant appears to be unused [UnusedResources]
    <color name="aether_on_surface_variant">#CBD5E1</color>
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\AetherProject\Aether Client\app\src\main\res\values\colors.xml:27: Warning: The resource R.color.aether_neon_green appears to be unused [UnusedResources]
    <color name="aether_neon_green">#00FF9F</color>
           ~~~~~~~~~~~~~~~~~~~~~~~~
D:\AetherProject\Aether Client\app\src\main\res\values\colors.xml:28: Warning: The resource R.color.aether_neon_purple appears to be unused [UnusedResources]
    <color name="aether_neon_purple">#9945FF</color>
           ~~~~~~~~~~~~~~~~~~~~~~~~~
D:\AetherProject\Aether Client\app\src\main\res\values\colors.xml:29: Warning: The resource R.color.aether_neon_blue appears to be unused [UnusedResources]
    <color name="aether_neon_blue">#14F0FF</color>
           ~~~~~~~~~~~~~~~~~~~~~~~
D:\AetherProject\Aether Client\app\src\main\res\values\colors.xml:30: Warning: The resource R.color.aether_neon_pink appears to be unused [UnusedResources]
    <color name="aether_neon_pink">#FF0A78</color>
           ~~~~~~~~~~~~~~~~~~~~~~~
D:\AetherProject\Aether Client\app\src\main\res\values\colors.xml:33: Warning: The resource R.color.aether_success appears to be unused [UnusedResources]
    <color name="aether_success">#10B981</color>
           ~~~~~~~~~~~~~~~~~~~~~
D:\AetherProject\Aether Client\app\src\main\res\values\colors.xml:34: Warning: The resource R.color.aether_warning appears to be unused [UnusedResources]
    <color name="aether_warning">#F59E0B</color>
           ~~~~~~~~~~~~~~~~~~~~~
D:\AetherProject\Aether Client\app\src\main\res\values\colors.xml:35: Warning: The resource R.color.aether_error appears to be unused [UnusedResources]
    <color name="aether_error">#EF4444</color>
           ~~~~~~~~~~~~~~~~~~~
D:\AetherProject\Aether Client\app\src\main\res\values\colors.xml:36: Warning: The resource R.color.aether_info appears to be unused [UnusedResources]
    <color name="aether_info">#3B82F6</color>
           ~~~~~~~~~~~~~~~~~~
D:\AetherProject\Aether Client\app\src\main\res\values\strings.xml:5: Warning: The resource R.string.third_party_licenses_title appears to be unused [UnusedResources]
    <string name="third_party_licenses_title">Third-Party Licenses</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\AetherProject\Aether Client\app\src\main\res\values\strings.xml:6: Warning: The resource R.string.third_party_licenses_description appears to be unused [UnusedResources]
    <string name="third_party_licenses_description">AetherClient uses several open source libraries. We acknowledge and thank all contributors to these projects.</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\AetherProject\Aether Client\app\src\main\res\values\strings.xml:7: Warning: The resource R.string.home appears to be unused [UnusedResources]
    <string name="home">Home</string>
            ~~~~~~~~~~~
D:\AetherProject\Aether Client\app\src\main\res\values\strings.xml:8: Warning: The resource R.string.what_is_this appears to be unused [UnusedResources]
    <string name="what_is_this">What\'s this?</string>
            ~~~~~~~~~~~~~~~~~~~
D:\AetherProject\Aether Client\app\src\main\res\values\strings.xml:9: Warning: The resource R.string.introduction appears to be unused [UnusedResources]
    <string name="introduction">AetherClient is a Minecraft Bedrock client that utilizes a MITM approach, providing various utilities to enhance the gameplay experience without modifying the game\'s memory.</string>
            ~~~~~~~~~~~~~~~~~~~
D:\AetherProject\Aether Client\app\src\main\res\values\strings.xml:10: Warning: The resource R.string.backend appears to be unused [UnusedResources]
    <string name="backend" translatable="false">AetherRelay</string>
            ~~~~~~~~~~~~~~
D:\AetherProject\Aether Client\app\src\main\res\values\strings.xml:11: Warning: The resource R.string.backend_introduction appears to be unused [UnusedResources]
    <string name="backend_introduction">Set up a MITM-based interception in a few simple steps.</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\AetherProject\Aether Client\app\src\main\res\values\strings.xml:12: Warning: The resource R.string.minecraft appears to be unused [UnusedResources]
    <string name="minecraft" translatable="false">Minecraft</string>
            ~~~~~~~~~~~~~~~~
D:\AetherProject\Aether Client\app\src\main\res\values\strings.xml:13: Warning: The resource R.string.recommended_version appears to be unused [UnusedResources]
    <string name="recommended_version">Recommended version: %s</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\AetherProject\Aether Client\app\src\main\res\values\strings.xml:14: Warning: The resource R.string.capturing_game_packets appears to be unused [UnusedResources]
    <string name="capturing_game_packets">Capturing game packets...</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\AetherProject\Aether Client\app\src\main\res\values\strings.xml:15: Warning: The resource R.string.stop appears to be unused [UnusedResources]
    <string name="stop">Stop</string>
            ~~~~~~~~~~~
D:\AetherProject\Aether Client\app\src\main\res\values\strings.xml:16: Warning: The resource R.string.overlay_permission_denied appears to be unused [UnusedResources]
    <string name="overlay_permission_denied">Overlay permission denied</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\AetherProject\Aether Client\app\src\main\res\values\strings.xml:17: Warning: The resource R.string.notification_permission_denied appears to be unused [UnusedResources]
    <string name="notification_permission_denied">Notification permission denied</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\AetherProject\Aether Client\app\src\main\res\values\strings.xml:18: Warning: The resource R.string.request_overlay_permission appears to be unused [UnusedResources]
    <string name="request_overlay_permission">We need overlay permission to display the function floating window</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\AetherProject\Aether Client\app\src\main\res\values\strings.xml:19: Warning: The resource R.string.game_settings appears to be unused [UnusedResources]
    <string name="game_settings">Game Settings</string>
            ~~~~~~~~~~~~~~~~~~~~
D:\AetherProject\Aether Client\app\src\main\res\values\strings.xml:20: Warning: The resource R.string.confirm appears to be unused [UnusedResources]
    <string name="confirm">Confirm</string>
            ~~~~~~~~~~~~~~
D:\AetherProject\Aether Client\app\src\main\res\values\strings.xml:21: Warning: The resource R.string.cancel appears to be unused [UnusedResources]
    <string name="cancel">Cancel</string>
            ~~~~~~~~~~~~~
D:\AetherProject\Aether Client\app\src\main\res\values\strings.xml:22: Warning: The resource R.string.server_host_name appears to be unused [UnusedResources]
    <string name="server_host_name">Server host name</string>
            ~~~~~~~~~~~~~~~~~~~~~~~
D:\AetherProject\Aether Client\app\src\main\res\values\strings.xml:23: Warning: The resource R.string.server_port appears to be unused [UnusedResources]
    <string name="server_port">Server port</string>
            ~~~~~~~~~~~~~~~~~~
D:\AetherProject\Aether Client\app\src\main\res\values\strings.xml:24: Warning: The resource R.string.tips appears to be unused [UnusedResources]
    <string name="tips">Tips</string>
            ~~~~~~~~~~~
D:\AetherProject\Aether Client\app\src\main\res\values\strings.xml:25: Warning: The resource R.string.change_game_settings_tip appears to be unused [UnusedResources]
    <string name="change_game_settings_tip">You need to disconnect before changing game settings.</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\AetherProject\Aether Client\app\src\main\res\values\strings.xml:26: Warning: The resource R.string.backend_connected appears to be unused [UnusedResources]
    <string name="backend_connected">AetherRelay connected.</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~
D:\AetherProject\Aether Client\app\src\main\res\values\strings.xml:27: Warning: The resource R.string.start_game appears to be unused [UnusedResources]
    <string name="start_game">Start Game</string>
            ~~~~~~~~~~~~~~~~~
D:\AetherProject\Aether Client\app\src\main\res\values\strings.xml:28: Warning: The resource R.string.backend_disconnected appears to be unused [UnusedResources]
    <string name="backend_disconnected">AetherRelay disconnected.</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\AetherProject\Aether Client\app\src\main\res\values\strings.xml:29: Warning: The resource R.string.select_game appears to be unused [UnusedResources]
    <string name="select_game">Select game</string>
            ~~~~~~~~~~~~~~~~~~
D:\AetherProject\Aether Client\app\src\main\res\values\strings.xml:30: Warning: The resource R.string.no_game_selected appears to be unused [UnusedResources]
    <string name="no_game_selected">No game selected</string>
            ~~~~~~~~~~~~~~~~~~~~~~~
D:\AetherProject\Aether Client\app\src\main\res\values\strings.xml:31: Warning: The resource R.string.game_selector appears to be unused [UnusedResources]
    <string name="game_selector">Game Selector</string>
            ~~~~~~~~~~~~~~~~~~~~
D:\AetherProject\Aether Client\app\src\main\res\values\strings.xml:32: Warning: The resource R.string.select_game_first appears to be unused [UnusedResources]
    <string name="select_game_first">Please select a game first.</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~
D:\AetherProject\Aether Client\app\src\main\res\values\strings.xml:33: Warning: The resource R.string.failed_to_launch_game appears to be unused [UnusedResources]
    <string name="failed_to_launch_game">Failed to launch the game.</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\AetherProject\Aether Client\app\src\main\res\values\strings.xml:34: Warning: The resource R.string.account appears to be unused [UnusedResources]
    <string name="account">Account</string>
            ~~~~~~~~~~~~~~
D:\AetherProject\Aether Client\app\src\main\res\values\strings.xml:35: Warning: The resource R.string.add_account appears to be unused [UnusedResources]
    <string name="add_account">Add Account</string>
            ~~~~~~~~~~~~~~~~~~
D:\AetherProject\Aether Client\app\src\main\res\values\strings.xml:36: Warning: The resource R.string.fetch_account_failed appears to be unused [UnusedResources]
    <string name="fetch_account_failed">Fetch account failed: %s</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\AetherProject\Aether Client\app\src\main\res\values\strings.xml:37: Warning: The resource R.string.delete appears to be unused [UnusedResources]
    <string name="delete">Delete</string>
            ~~~~~~~~~~~~~
D:\AetherProject\Aether Client\app\src\main\res\values\strings.xml:38: Warning: The resource R.string.select appears to be unused [UnusedResources]
    <string name="select">Select</string>
            ~~~~~~~~~~~~~
D:\AetherProject\Aether Client\app\src\main\res\values\strings.xml:39: Warning: The resource R.string.unselect appears to be unused [UnusedResources]
    <string name="unselect">Unselect</string>
            ~~~~~~~~~~~~~~~
D:\AetherProject\Aether Client\app\src\main\res\values\strings.xml:40: Warning: The resource R.string.has_been_selected appears to be unused [UnusedResources]
    <string name="has_been_selected">(Selected)</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~
D:\AetherProject\Aether Client\app\src\main\res\values\strings.xml:41: Warning: The resource R.string.server appears to be unused [UnusedResources]
    <string name="server">Servers</string>
            ~~~~~~~~~~~~~
D:\AetherProject\Aether Client\app\src\main\res\values\strings.xml:42: Warning: The resource R.string.about appears to be unused [UnusedResources]
    <string name="about">About</string>
            ~~~~~~~~~~~~
D:\AetherProject\Aether Client\app\src\main\res\values\strings.xml:43: Warning: The resource R.string.settings appears to be unused [UnusedResources]
    <string name="settings">Settings</string>
            ~~~~~~~~~~~~~~~
D:\AetherProject\Aether Client\app\src\main\res\values\strings.xml:44: Warning: The resource R.string.how_do_i_switch_login_mode appears to be unused [UnusedResources]
    <string name="how_do_i_switch_login_mode">How do I switch login modes?</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\AetherProject\Aether Client\app\src\main\res\values\strings.xml:45: Warning: The resource R.string.login_mode_introduction appears to be unused [UnusedResources]
    <string name="login_mode_introduction">If you want to log in to online mode, add an account and select it. If you want to log in to offline mode, deselect the account.</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\AetherProject\Aether Client\app\src\main\res\values\strings.xml:46: Warning: The resource R.string.combat appears to be unused [UnusedResources]
    <string name="combat">Combat</string>
            ~~~~~~~~~~~~~
D:\AetherProject\Aether Client\app\src\main\res\values\strings.xml:47: Warning: The resource R.string.motion appears to be unused [UnusedResources]
    <string name="motion">Motion</string>
            ~~~~~~~~~~~~~
D:\AetherProject\Aether Client\app\src\main\res\values\strings.xml:48: Warning: The resource R.string.visual appears to be unused [UnusedResources]
    <string name="visual">Visual</string>
            ~~~~~~~~~~~~~
D:\AetherProject\Aether Client\app\src\main\res\values\strings.xml:49: Warning: The resource R.string.particle appears to be unused [UnusedResources]
    <string name="particle">Particle</string>
            ~~~~~~~~~~~~~~~
D:\AetherProject\Aether Client\app\src\main\res\values\strings.xml:50: Warning: The resource R.string.misc appears to be unused [UnusedResources]
    <string name="misc">Misc</string>
            ~~~~~~~~~~~
D:\AetherProject\Aether Client\app\src\main\res\values\strings.xml:51: Warning: The resource R.string.config appears to be unused [UnusedResources]
    <string name="config">Config</string>
            ~~~~~~~~~~~~~
D:\AetherProject\Aether Client\app\src\main\res\values\strings.xml:52: Warning: The resource R.string.overlay_opacity appears to be unused [UnusedResources]
    <string name="overlay_opacity">Overlay Button Opacity</string>
            ~~~~~~~~~~~~~~~~~~~~~~
D:\AetherProject\Aether Client\app\src\main\res\values\strings.xml:53: Warning: The resource R.string.shortcut_opacity appears to be unused [UnusedResources]
    <string name="shortcut_opacity">Shortcut Button Opacity</string>
            ~~~~~~~~~~~~~~~~~~~~~~~
D:\AetherProject\Aether Client\app\src\main\res\values\strings.xml:54: Warning: The resource R.string.overlay_icon appears to be unused [UnusedResources]
    <string name="overlay_icon">Overlay Icon</string>
            ~~~~~~~~~~~~~~~~~~~
D:\AetherProject\Aether Client\app\src\main\res\values\strings.xml:55: Warning: The resource R.string.overlay_icon_description appears to be unused [UnusedResources]
    <string name="overlay_icon_description">Change the floating button icon</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\AetherProject\Aether Client\app\src\main\res\values\strings.xml:56: Warning: The resource R.string.overlay_opacity_settings appears to be unused [UnusedResources]
    <string name="overlay_opacity_settings">Opacity Settings</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\AetherProject\Aether Client\app\src\main\res\values\strings.xml:57: Warning: The resource R.string.overlay_opacity_description appears to be unused [UnusedResources]
    <string name="overlay_opacity_description">Adjust transparency of overlay and shortcut buttons</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\AetherProject\Aether Client\app\src\main\res\values\strings.xml:58: Warning: The resource R.string.overlay_border_color appears to be unused [UnusedResources]
    <string name="overlay_border_color">Overlay Border Color</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\AetherProject\Aether Client\app\src\main\res\values\strings.xml:59: Warning: The resource R.string.overlay_border_color_description appears to be unused [UnusedResources]
    <string name="overlay_border_color_description">Change the border color of the floating button</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\AetherProject\Aether Client\app\src\main\res\values\strings.xml:60: Warning: The resource R.string.request_ignore_battery_optimization appears to be unused [UnusedResources]
    <string name="request_ignore_battery_optimization">We need to ignore battery optimization to stay connected</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\AetherProject\Aether Client\app\src\main\res\values\strings.xml:61: Warning: The resource R.string.ignore_battery_optimization_denied appears to be unused [UnusedResources]
    <string name="ignore_battery_optimization_denied">Ignore battery optimization permission denied</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\AetherProject\Aether Client\app\src\main\res\values\strings.xml:62: Warning: The resource R.string.shortcut appears to be unused [UnusedResources]
    <string name="shortcut">Shortcut</string>
            ~~~~~~~~~~~~~~~
D:\AetherProject\Aether Client\app\src\main\res\values\strings.xml:63: Warning: The resource R.string.check_updates_and_news appears to be unused [UnusedResources]
    <string name="check_updates_and_news">View Updates &amp; News</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\AetherProject\Aether Client\app\src\main\res\values\strings.xml:64: Warning: The resource R.string.crash_happened appears to be unused [UnusedResources]
    <string name="crash_happened">Crash Happened</string>
            ~~~~~~~~~~~~~~~~~~~~~
D:\AetherProject\Aether Client\app\src\main\res\values\strings.xml:65: Warning: The resource R.string.cannot_back appears to be unused [UnusedResources]
    <string name="cannot_back">A crash has occurred and you are not allowed to return. You can only restart the app.</string>
            ~~~~~~~~~~~~~~~~~~
D:\AetherProject\Aether Client\app\src\main\res\values\strings.xml:66: Warning: The resource R.string.effect appears to be unused [UnusedResources]
    <string name="effect">Effect</string>
            ~~~~~~~~~~~~~
D:\AetherProject\Aether Client\app\src\main\res\values\strings.xml:67: Warning: The resource R.string.login_in appears to be unused [UnusedResources]
    <string name="login_in">Log in using %s</string>
            ~~~~~~~~~~~~~~~
D:\AetherProject\Aether Client\app\src\main\res\values\strings.xml:68: Warning: The resource R.string.fetch_account_successfully appears to be unused [UnusedResources]
    <string name="fetch_account_successfully">Fetch account successfully</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\AetherProject\Aether Client\app\src\main\res\values\strings.xml:69: Warning: The resource R.string.xbox_device_code appears to be unused [UnusedResources]
    <string name="xbox_device_code">Xbox device code</string>
            ~~~~~~~~~~~~~~~~~~~~~~~
D:\AetherProject\Aether Client\app\src\main\res\values\strings.xml:70: Warning: The resource R.string.refresh appears to be unused [UnusedResources]
    <string name="refresh">Refresh</string>
            ~~~~~~~~~~~~~~

   Explanation for issues of type "UnusedResources":
   Unused resources make applications larger and slow down builds.


   The unused resource check can ignore tests. If you want to include
   resources that are only referenced from tests, consider packaging them in a
   test source set instead.

   You can include test sources in the unused resource check by setting the
   system property lint.unused-resources.include-tests =true, and to exclude
   them (usually for performance reasons), use
   lint.unused-resources.exclude-tests =true.
   ,

   Available options:

   **skip-libraries** (default is true):
   Whether the unused resource check should skip reporting unused resources in libraries.

   Many libraries will declare resources that are part of the library surface; other modules depending on the library will also reference the resources. To avoid reporting all these resources as unused (in the context of a library), the unused resource check normally skips reporting unused resources in libraries. Instead, run the unused resource check on the consuming app module (along with `checkDependencies=true`).

   However, there are cases where you want to check that all the resources declared in a library are used; in that case, you can disable the skip option.

   To configure this option, use a `lint.xml` file with an <option> like this:

   ```xml
   <lint>
       <issue id="UnusedResources">
           <option name="skip-libraries" value="true" />
       </issue>
   </lint>
   ```

D:\AetherProject\Aether Client\app\src\main\res\values\strings.xml:14: Warning: Replace "..." with ellipsis character (…, &#8230;) ? [TypographyEllipsis]
    <string name="capturing_game_packets">Capturing game packets...</string>
                                          ~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "TypographyEllipsis":
   You can replace the string "..." with a dedicated ellipsis character,
   ellipsis character (u2026, &#8230;). This can help make the text more
   readable.

   https://en.wikipedia.org/wiki/Ellipsis

D:\AetherProject\Aether Client\app\src\main\res\drawable\ic_launcher.png: Warning: Launcher icons should not fill every pixel of their square region; see the design guide for details [IconLauncherShape]

   Explanation for issues of type "IconLauncherShape":
   According to the Android Design Guide
   (https://d.android.com/r/studio-ui/designer/material/iconography) your
   launcher icons should "use a distinct silhouette", a "three-dimensional,
   front view, with a slight perspective as if viewed from above, so that
   users perceive some depth."

   The unique silhouette implies that your launcher icon should not be a
   filled square.

D:\AetherProject\Aether Client\app\src\main\res\mipmap-anydpi-v26\ic_launcher.xml:2: Warning: The application adaptive icon is missing a monochrome tag [MonochromeLauncherIcon]
<adaptive-icon xmlns:android="http://schemas.android.com/apk/res/android">
^
D:\AetherProject\Aether Client\app\src\main\res\mipmap-anydpi-v26\ic_launcher_round.xml:2: Warning: The application adaptive roundIcon is missing a monochrome tag [MonochromeLauncherIcon]
<adaptive-icon xmlns:android="http://schemas.android.com/apk/res/android">
^

   Explanation for issues of type "MonochromeLauncherIcon":
   If android:roundIcon and android:icon are both in your manifest, you must
   either remove the reference to android:roundIcon if it is not needed; or,
   supply the monochrome icon in the drawable defined by the android:roundIcon
   and android:icon attribute.

   For example, if android:roundIcon and android:icon are both in the
   manifest, a launcher might choose to use android:roundIcon over
   android:icon to display the adaptive app icon. Therefore, your themed
   application iconwill not show if your monochrome attribute is not also
   specified in android:roundIcon.

D:\AetherProject\Aether Client\app\src\main\res\drawable\ic_launcher.png: Warning: Found bitmap drawable res/drawable/ic_launcher.png in densityless folder [IconLocation]

   Explanation for issues of type "IconLocation":
   The res/drawable folder is intended for density-independent graphics such
   as shapes defined in XML. For bitmaps, move it to drawable-mdpi and
   consider providing higher and lower resolution versions in drawable-ldpi,
   drawable-hdpi and drawable-xhdpi. If the icon really is density independent
   (for example a solid color) you can place it in drawable-nodpi.

   https://developer.android.com/guide/practices/screens_support.html

0 errors, 109 warnings
