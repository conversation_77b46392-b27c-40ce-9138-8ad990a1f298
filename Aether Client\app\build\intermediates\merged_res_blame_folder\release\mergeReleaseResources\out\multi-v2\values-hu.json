{"logs": [{"outputFile": "com.radiantbyte.aetherclient.app-mergeReleaseResources-46:/values-hu/values-hu.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\d61819a9c261b5407a6ee59c046430b9\\transformed\\ui-release\\res\\values-hu\\values-hu.xml", "from": {"startLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "193,288,376,473,572,659,741,837,926,1013,1096,1184,1258,1351,1426,1497,1567,1646,1712", "endColumns": "94,87,96,98,86,81,95,88,86,82,87,73,92,74,70,69,78,65,120", "endOffsets": "283,371,468,567,654,736,832,921,1008,1091,1179,1253,1346,1421,1492,1562,1641,1707,1828"}, "to": {"startLines": "10,11,12,13,14,15,16,74,75,76,77,78,79,80,81,82,84,85,86", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "914,1009,1097,1194,1293,1380,1462,7888,7977,8064,8147,8235,8309,8402,8477,8548,8719,8798,8864", "endColumns": "94,87,96,98,86,81,95,88,86,82,87,73,92,74,70,69,78,65,120", "endOffsets": "1004,1092,1189,1288,1375,1457,1553,7972,8059,8142,8230,8304,8397,8472,8543,8613,8793,8859,8980"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\7abcdc53ecef1f011b8484f7921702b8\\transformed\\foundation-release\\res\\values-hu\\values-hu.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,142,231", "endColumns": "86,88,96", "endOffsets": "137,226,323"}, "to": {"startLines": "2,87,88", "startColumns": "4,4,4", "startOffsets": "105,8985,9074", "endColumns": "86,88,96", "endOffsets": "187,9069,9166"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\76f6aeeb7c72cabab8c29d18f4d3161d\\transformed\\core-1.16.0\\res\\values-hu\\values-hu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,356,457,560,667,777", "endColumns": "96,101,101,100,102,106,109,100", "endOffsets": "147,249,351,452,555,662,772,873"}, "to": {"startLines": "3,4,5,6,7,8,9,83", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "192,289,391,493,594,697,804,8618", "endColumns": "96,101,101,100,102,106,109,100", "endOffsets": "284,386,488,589,692,799,909,8714"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\13ceaa855c6ae6b23241ae42f3bae2bf\\transformed\\material3-release\\res\\values-hu\\values-hu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,176,292,400,516,611,708,822,962,1085,1232,1317,1417,1515,1617,1739,1876,1981,2121,2259,2385,2581,2704,2826,2948,3074,3173,3268,3387,3524,3626,3737,3841,3986,4133,4240,4347,4431,4529,4623,4731,4819,4906,5007,5088,5171,5270,5376,5471,5574,5660,5769,5867,5973,6094,6175,6287", "endColumns": "120,115,107,115,94,96,113,139,122,146,84,99,97,101,121,136,104,139,137,125,195,122,121,121,125,98,94,118,136,101,110,103,144,146,106,106,83,97,93,107,87,86,100,80,82,98,105,94,102,85,108,97,105,120,80,111,97", "endOffsets": "171,287,395,511,606,703,817,957,1080,1227,1312,1412,1510,1612,1734,1871,1976,2116,2254,2380,2576,2699,2821,2943,3069,3168,3263,3382,3519,3621,3732,3836,3981,4128,4235,4342,4426,4524,4618,4726,4814,4901,5002,5083,5166,5265,5371,5466,5569,5655,5764,5862,5968,6089,6170,6282,6380"}, "to": {"startLines": "17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1558,1679,1795,1903,2019,2114,2211,2325,2465,2588,2735,2820,2920,3018,3120,3242,3379,3484,3624,3762,3888,4084,4207,4329,4451,4577,4676,4771,4890,5027,5129,5240,5344,5489,5636,5743,5850,5934,6032,6126,6234,6322,6409,6510,6591,6674,6773,6879,6974,7077,7163,7272,7370,7476,7597,7678,7790", "endColumns": "120,115,107,115,94,96,113,139,122,146,84,99,97,101,121,136,104,139,137,125,195,122,121,121,125,98,94,118,136,101,110,103,144,146,106,106,83,97,93,107,87,86,100,80,82,98,105,94,102,85,108,97,105,120,80,111,97", "endOffsets": "1674,1790,1898,2014,2109,2206,2320,2460,2583,2730,2815,2915,3013,3115,3237,3374,3479,3619,3757,3883,4079,4202,4324,4446,4572,4671,4766,4885,5022,5124,5235,5339,5484,5631,5738,5845,5929,6027,6121,6229,6317,6404,6505,6586,6669,6768,6874,6969,7072,7158,7267,7365,7471,7592,7673,7785,7883"}}]}]}