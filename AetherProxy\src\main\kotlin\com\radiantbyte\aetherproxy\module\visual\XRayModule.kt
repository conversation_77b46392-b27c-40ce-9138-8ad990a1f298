package com.radiantbyte.aetherproxy.module.visual

import com.radiantbyte.aetherproxy.event.handler.packet
import com.radiantbyte.aetherproxy.module.Module
import com.radiantbyte.aetherproxy.module.ModuleCategory
import com.radiantbyte.aetherproxy.session.AetherSession
import com.radiantbyte.aetherproxy.util.RuntimeBlockStates
import com.radiantbyte.aetherproxy.util.command
import com.radiantbyte.aetherproxy.util.mismatch
import io.netty.buffer.ByteBuf
import io.netty.buffer.Unpooled
import org.cloudburstmc.protocol.bedrock.packet.LevelChunkPacket
import org.cloudburstmc.protocol.bedrock.packet.UpdateBlockPacket
import org.cloudburstmc.protocol.bedrock.packet.StartGamePacket
import org.cloudburstmc.protocol.bedrock.packet.UpdateSubChunkBlocksPacket
import org.cloudburstmc.protocol.bedrock.data.definitions.BlockDefinition
import java.util.concurrent.atomic.AtomicLong
import java.util.concurrent.ConcurrentHashMap
import java.time.LocalDateTime
import java.time.format.DateTimeFormatter

/**
 * XRay module that makes blocks transparent to reveal valuable resources
 * Replaces unwanted blocks with air while preserving valuable blocks
 */
class XRayModule(aetherSession: AetherSession) : Module(aetherSession, "XRay", ModuleCategory.Visual) {

    // Configuration options
    private val enableOres by boolValue("enable_ores", true)
    private val enableChests by boolValue("enable_chests", true)
    private val enableSpawners by boolValue("enable_spawners", true)
    private val enableFluids by boolValue("enable_fluids", true)
    private val enableBedrock by boolValue("enable_bedrock", true)
    private val logBlockReplacements by boolValue("log_replacements", false)
    private val maxLoggedReplacements by intValue("max_logged_replacements", 100, 0..1000)
    private val detailedLogging by boolValue("detailed_logging", false)
    private val logPerformance by boolValue("log_performance", false)

    // Statistics
    private val blocksProcessed = AtomicLong(0)
    private val blocksReplaced = AtomicLong(0)
    private val chunksProcessed = AtomicLong(0)
    private val errorsEncountered = AtomicLong(0)
    private var loggedReplacements = 0

    // Performance tracking
    private val processingTimes = mutableListOf<Long>()
    private var lastPerformanceLog = System.currentTimeMillis()
    private val performanceLogInterval = 30000L // 30 seconds

    // Block replacement tracking
    private val blockReplacementCounts = ConcurrentHashMap<String, AtomicLong>()
    private val recentReplacements = mutableListOf<BlockReplacement>()
    private val maxRecentReplacements = 50

    // XRay state
    private var xrayEnabled = false
    private var airBlockDefinition: BlockDefinition? = null
    private var startTime: Long = 0

    // Data classes for logging
    private data class BlockReplacement(
        val originalBlock: String,
        val position: String,
        val timestamp: LocalDateTime
    )

    init {
        command(
            "xray",
            "Toggle XRay mode to see through blocks and reveal valuable resources",
            handler = { arguments ->
                when (arguments.size) {
                    0 -> toggle()
                    1 -> when (arguments[0].lowercase()) {
                        "stats" -> showStats()
                        "reset" -> resetStats()
                        "blocks" -> showBlockLists()
                        "debug" -> showDebugInfo()
                        "performance" -> showPerformanceStats()
                        "recent" -> showRecentReplacements()
                        "errors" -> showErrorLog()
                        else -> mismatch()
                    }
                    else -> mismatch()
                }
            },
            helps = arrayOf(
                arrayOf("Toggle XRay mode"),
                arrayOf("stats", "Show XRay statistics"),
                arrayOf("reset", "Reset XRay statistics"),
                arrayOf("blocks", "Show visible/hidden block lists"),
                arrayOf("debug", "Show debug information"),
                arrayOf("performance", "Show performance statistics"),
                arrayOf("recent", "Show recent block replacements"),
                arrayOf("errors", "Show error log")
            )
        )

        // Initialize block definitions when game starts
        packet<StartGamePacket> { packetEvent, _ ->
            val packet = packetEvent.packet
            try {
                // Get block palette from the packet
                val blockPalette = packet.blockPalette
                if (blockPalette != null) {
                    // Convert NBT block palette to block definitions for RuntimeBlockStates
                    val blockDefinitions = mutableListOf<org.cloudburstmc.protocol.bedrock.data.definitions.BlockDefinition>()

                    // Create simple block definitions from the palette
                    for ((index, blockNbt) in blockPalette.withIndex()) {
                        val blockData = blockNbt.getCompound("block")
                        val blockName = blockData.getString("name")

                        // Create a simple block definition using SimpleBlockDefinition
                        val blockDef = org.cloudburstmc.protocol.bedrock.data.definitions.SimpleBlockDefinition(
                            blockName,
                            index,
                            blockNbt
                        )

                        blockDefinitions.add(blockDef)

                        // Cache air block definition
                        if (blockName == "minecraft:air") {
                            airBlockDefinition = blockDef
                        }
                    }

                    RuntimeBlockStates.initialize(blockDefinitions)
                    displayClientMessage("§a[XRay] Block definitions initialized: ${blockDefinitions.size} blocks")
                    if (airBlockDefinition != null) {
                        displayClientMessage("§a[XRay] Air block cached (ID: ${airBlockDefinition!!.runtimeId})")
                    } else {
                        displayClientMessage("§c[XRay] Warning: Air block not found!")
                    }
                } else {
                    displayClientMessage("§c[XRay] Warning: No block palette found in StartGamePacket!")
                }
            } catch (e: Exception) {
                displayClientMessage("§c[XRay] Error initializing block definitions: ${e.message}")
                e.printStackTrace()
            }
        }

        // Process chunk packets for XRay
        packet<LevelChunkPacket> { packetEvent, _ ->
            if (!isEnabled()) return@packet
            
            try {
                val packet = packetEvent.packet
                processChunkPacket(packet)
                chunksProcessed.incrementAndGet()
            } catch (e: Exception) {
                displayClientMessage("§c[XRay] Error processing chunk: ${e.message}")
                if (logBlockReplacements) {
                    e.printStackTrace()
                }
            }
        }

        // Process individual block updates
        packet<UpdateBlockPacket> { packetEvent, _ ->
            if (!isEnabled()) return@packet
            
            try {
                val packet = packetEvent.packet
                processBlockUpdate(packet)
            } catch (e: Exception) {
                displayClientMessage("§c[XRay] Error processing block update: ${e.message}")
                if (logBlockReplacements) {
                    e.printStackTrace()
                }
            }
        }

        // Process sub-chunk block updates
        packet<UpdateSubChunkBlocksPacket> { packetEvent, _ ->
            if (!isEnabled()) return@packet
            
            try {
                val packet = packetEvent.packet
                processSubChunkBlockUpdates(packet)
            } catch (e: Exception) {
                displayClientMessage("§c[XRay] Error processing sub-chunk updates: ${e.message}")
                if (logBlockReplacements) {
                    e.printStackTrace()
                }
            }
        }
    }

    // Track module state changes
    private var wasEnabled = false

    init {
        // Monitor state changes in packet handler
        packet<org.cloudburstmc.protocol.bedrock.packet.PlayerAuthInputPacket> { _, _ ->
            val currentlyEnabled = isEnabled()
            if (currentlyEnabled != wasEnabled) {
                if (currentlyEnabled) {
                    onEnable()
                } else {
                    onDisable()
                }
                wasEnabled = currentlyEnabled
            }
        }
    }

    private fun onEnable() {
        xrayEnabled = true
        startTime = System.currentTimeMillis()
        resetStats()
        displayClientMessage("§a[XRay] Enabled - Valuable blocks will be revealed")
        displayClientMessage("§7[XRay] Use '/xray stats' to view statistics")
        displayClientMessage("§7[XRay] Use '/xray debug' for detailed information")

        if (detailedLogging) {
            displayClientMessage("§7[XRay] Detailed logging enabled")
        }
        if (logPerformance) {
            displayClientMessage("§7[XRay] Performance logging enabled")
        }
    }

    private fun onDisable() {
        xrayEnabled = false
        val duration = System.currentTimeMillis() - startTime
        displayClientMessage("§c[XRay] Disabled")
        displayClientMessage("§7[XRay] Session duration: ${formatDuration(duration)}")
        showStats()
    }

    /**
     * Process a chunk packet and modify block data for XRay
     */
    private fun processChunkPacket(packet: LevelChunkPacket) {
        val startTime = System.currentTimeMillis()
        val data = packet.data
        if (data == null || !data.isReadable) {
            return
        }

        try {
            // Create a copy of the data to modify
            val modifiedData = Unpooled.buffer(data.readableBytes())
            data.markReaderIndex()
            modifiedData.writeBytes(data)
            data.resetReaderIndex()

            // Process the chunk data
            val processedBlocks = processChunkData(modifiedData)
            blocksProcessed.addAndGet(processedBlocks.toLong())

            // Replace the packet data
            packet.data?.release()
            packet.data = modifiedData

            // Log performance
            val processingTime = System.currentTimeMillis() - startTime
            logPerformance(processingTime)

            if (detailedLogging && loggedReplacements < maxLoggedReplacements) {
                displayClientMessage("§7[XRay] Processed chunk (${packet.chunkX}, ${packet.chunkZ}) - $processedBlocks blocks in ${processingTime}ms")
                loggedReplacements++
            }
        } catch (e: Exception) {
            logError("Error modifying chunk data", e)
        }
    }

    /**
     * Process individual block update
     */
    private fun processBlockUpdate(packet: UpdateBlockPacket) {
        val definition = packet.definition
        if (definition == null) return

        // Get block name from NamedDefinition interface
        val blockName = if (definition is org.cloudburstmc.protocol.common.NamedDefinition) {
            definition.identifier
        } else {
            "unknown_block_${definition.runtimeId}"
        }

        blocksProcessed.incrementAndGet()

        if (shouldReplaceBlock(blockName)) {
            airBlockDefinition?.let { airDef ->
                packet.definition = airDef
                blocksReplaced.incrementAndGet()

                // Log the replacement
                logBlockReplacement(blockName, packet.blockPosition.toString())
            }
        }
    }

    /**
     * Process sub-chunk block updates
     */
    private fun processSubChunkBlockUpdates(packet: UpdateSubChunkBlocksPacket) {
        val standardBlocks = packet.standardBlocks
        val extraBlocks = packet.extraBlocks
        var replacedCount = 0
        val startTime = System.currentTimeMillis()

        // Process standard blocks
        for (block in standardBlocks) {
            val definition = block.definition
            if (definition == null) continue

            // Get block name from NamedDefinition interface
            val blockName = if (definition is org.cloudburstmc.protocol.common.NamedDefinition) {
                definition.identifier
            } else {
                "unknown_block_${definition.runtimeId}"
            }

            blocksProcessed.incrementAndGet()

            if (shouldReplaceBlock(blockName)) {
                airBlockDefinition?.let { airDef ->
                    // Create new BlockChangeEntry with air definition
                    val newEntry = org.cloudburstmc.protocol.bedrock.data.BlockChangeEntry(
                        block.position,
                        airDef,
                        block.updateFlags,
                        block.messageEntityId,
                        block.messageType
                    )
                    // Replace in the list
                    val index = standardBlocks.indexOf(block)
                    if (index >= 0) {
                        standardBlocks[index] = newEntry
                    }

                    blocksReplaced.incrementAndGet()
                    replacedCount++

                    // Log individual replacements if detailed logging is enabled
                    if (detailedLogging) {
                        logBlockReplacement(blockName, "${block.position}")
                    }
                }
            }
        }

        // Process extra blocks
        for (block in extraBlocks) {
            val definition = block.definition
            if (definition == null) continue

            // Get block name from NamedDefinition interface
            val blockName = if (definition is org.cloudburstmc.protocol.common.NamedDefinition) {
                definition.identifier
            } else {
                "unknown_block_${definition.runtimeId}"
            }

            blocksProcessed.incrementAndGet()

            if (shouldReplaceBlock(blockName)) {
                airBlockDefinition?.let { airDef ->
                    // Create new BlockChangeEntry with air definition
                    val newEntry = org.cloudburstmc.protocol.bedrock.data.BlockChangeEntry(
                        block.position,
                        airDef,
                        block.updateFlags,
                        block.messageEntityId,
                        block.messageType
                    )
                    // Replace in the list
                    val index = extraBlocks.indexOf(block)
                    if (index >= 0) {
                        extraBlocks[index] = newEntry
                    }

                    blocksReplaced.incrementAndGet()
                    replacedCount++

                    // Log individual replacements if detailed logging is enabled
                    if (detailedLogging) {
                        logBlockReplacement(blockName, "${block.position}")
                    }
                }
            }
        }

        // Log performance
        val processingTime = System.currentTimeMillis() - startTime
        logPerformance(processingTime)

        if (detailedLogging && replacedCount > 0 && loggedReplacements < maxLoggedReplacements) {
            displayClientMessage("§7[XRay] Sub-chunk update: replaced $replacedCount blocks in ${processingTime}ms")
            loggedReplacements++
        }
    }

    /**
     * Process chunk data buffer and replace blocks
     * This implementation attempts to modify block data in chunk packets
     */
    private fun processChunkData(data: ByteBuf): Int {
        var processedBlocks = 0

        try {
            // Save the current reader index
            val originalReaderIndex = data.readerIndex()

            // Try to process the chunk data
            // Note: Chunk format is complex and version-dependent
            // This is a simplified approach that may not work for all versions

            // Skip to potential block data section
            // The exact format depends on the protocol version
            if (data.readableBytes() < 4) {
                return 0
            }

            // For safety, we'll process this conservatively
            // and rely more on individual block updates
            processedBlocks = processChunkDataSafely(data)

            // Reset reader index if we couldn't process successfully
            if (processedBlocks == 0) {
                data.readerIndex(originalReaderIndex)
            }

        } catch (e: Exception) {
            if (logBlockReplacements) {
                displayClientMessage("§c[XRay] Chunk data processing error: ${e.message}")
            }
            // Reset to original position on error
            try {
                data.readerIndex(0)
            } catch (ignored: Exception) {}
        }

        return processedBlocks
    }

    /**
     * Safely process chunk data with error handling
     */
    private fun processChunkDataSafely(data: ByteBuf): Int {
        // For now, we'll use a conservative approach
        // The chunk format is too complex to reliably modify without
        // detailed knowledge of the specific protocol version

        // Instead, we'll focus on intercepting individual block updates
        // which is more reliable and safer

        return 0
    }

    /**
     * Determine if a block should be replaced with air in XRay mode
     */
    private fun shouldReplaceBlock(blockName: String): Boolean {
        // Never replace air blocks
        if (isAirBlock(blockName)) {
            return false
        }

        // Handle fluids based on configuration
        if (isFluidBlock(blockName)) {
            return !enableFluids
        }

        // Check if it's a valuable block that should remain visible
        if (RuntimeBlockStates.isXRayVisible(blockName)) {
            // Apply configuration filters to determine if valuable blocks should be hidden
            return when {
                isOreBlock(blockName) && !enableOres -> true
                isChestBlock(blockName) && !enableChests -> true
                isSpawnerBlock(blockName) && !enableSpawners -> true
                isBedrockBlock(blockName) && !enableBedrock -> true
                else -> false
            }
        }

        // Replace blocks that should be hidden (common blocks that obstruct view)
        return RuntimeBlockStates.isXRayHidden(blockName) || isCommonObstructingBlock(blockName)
    }

    /**
     * Check if a block is an air block
     */
    private fun isAirBlock(blockName: String): Boolean {
        return blockName in setOf(
            "minecraft:air",
            "minecraft:cave_air",
            "minecraft:void_air"
        )
    }

    /**
     * Check if a block is a fluid block
     */
    private fun isFluidBlock(blockName: String): Boolean {
        return blockName in setOf(
            "minecraft:water",
            "minecraft:flowing_water",
            "minecraft:lava",
            "minecraft:flowing_lava"
        )
    }

    /**
     * Check if a block is an ore block
     */
    private fun isOreBlock(blockName: String): Boolean {
        return blockName.contains("ore") || blockName == "minecraft:ancient_debris"
    }

    /**
     * Check if a block is a chest or storage block
     */
    private fun isChestBlock(blockName: String): Boolean {
        return blockName.contains("chest") ||
               blockName.contains("shulker_box") ||
               blockName == "minecraft:barrel" ||
               blockName == "minecraft:hopper" ||
               blockName == "minecraft:dropper" ||
               blockName == "minecraft:dispenser"
    }

    /**
     * Check if a block is a spawner
     */
    private fun isSpawnerBlock(blockName: String): Boolean {
        return blockName == "minecraft:spawner" || blockName == "minecraft:mob_spawner"
    }

    /**
     * Check if a block is bedrock
     */
    private fun isBedrockBlock(blockName: String): Boolean {
        return blockName == "minecraft:bedrock"
    }

    /**
     * Check if a block is a common obstructing block that should be hidden
     */
    private fun isCommonObstructingBlock(blockName: String): Boolean {
        return blockName in setOf(
            // Stone variants
            "minecraft:stone",
            "minecraft:granite",
            "minecraft:polished_granite",
            "minecraft:diorite",
            "minecraft:polished_diorite",
            "minecraft:andesite",
            "minecraft:polished_andesite",
            "minecraft:deepslate",
            "minecraft:cobblestone",
            "minecraft:cobbled_deepslate",
            "minecraft:mossy_cobblestone",
            "minecraft:stone_bricks",
            "minecraft:mossy_stone_bricks",
            "minecraft:cracked_stone_bricks",
            "minecraft:chiseled_stone_bricks",

            // Dirt and grass
            "minecraft:dirt",
            "minecraft:coarse_dirt",
            "minecraft:podzol",
            "minecraft:grass_block",
            "minecraft:mycelium",
            "minecraft:rooted_dirt",

            // Sand variants
            "minecraft:sand",
            "minecraft:red_sand",
            "minecraft:sandstone",
            "minecraft:red_sandstone",
            "minecraft:cut_sandstone",
            "minecraft:cut_red_sandstone",
            "minecraft:chiseled_sandstone",
            "minecraft:chiseled_red_sandstone",
            "minecraft:smooth_sandstone",
            "minecraft:smooth_red_sandstone",

            // Other common blocks
            "minecraft:gravel",
            "minecraft:clay",
            "minecraft:terracotta",
            "minecraft:netherrack",
            "minecraft:end_stone",
            "minecraft:tuff",
            "minecraft:calcite",
            "minecraft:dripstone_block",
            "minecraft:pointed_dripstone",

            // Wood blocks (logs, planks, etc.)
            "minecraft:oak_log",
            "minecraft:birch_log",
            "minecraft:spruce_log",
            "minecraft:jungle_log",
            "minecraft:acacia_log",
            "minecraft:dark_oak_log",
            "minecraft:mangrove_log",
            "minecraft:cherry_log",
            "minecraft:oak_wood",
            "minecraft:birch_wood",
            "minecraft:spruce_wood",
            "minecraft:jungle_wood",
            "minecraft:acacia_wood",
            "minecraft:dark_oak_wood",
            "minecraft:mangrove_wood",
            "minecraft:cherry_wood",
            "minecraft:stripped_oak_log",
            "minecraft:stripped_birch_log",
            "minecraft:stripped_spruce_log",
            "minecraft:stripped_jungle_log",
            "minecraft:stripped_acacia_log",
            "minecraft:stripped_dark_oak_log",
            "minecraft:stripped_mangrove_log",
            "minecraft:stripped_cherry_log",

            // Leaves
            "minecraft:oak_leaves",
            "minecraft:birch_leaves",
            "minecraft:spruce_leaves",
            "minecraft:jungle_leaves",
            "minecraft:acacia_leaves",
            "minecraft:dark_oak_leaves",
            "minecraft:mangrove_leaves",
            "minecraft:cherry_leaves",
            "minecraft:azalea_leaves",
            "minecraft:azalea_leaves_flowered"
        )
    }

    /**
     * Show XRay statistics
     */
    private fun showStats() {
        displayClientMessage("§6=== XRay Statistics ===")
        displayClientMessage("§7Chunks processed: §f${chunksProcessed.get()}")
        displayClientMessage("§7Blocks processed: §f${blocksProcessed.get()}")
        displayClientMessage("§7Blocks replaced: §f${blocksReplaced.get()}")
        val replacementRate = if (blocksProcessed.get() > 0) {
            (blocksReplaced.get() * 100.0 / blocksProcessed.get())
        } else 0.0
        displayClientMessage("§7Replacement rate: §f${"%.2f".format(replacementRate)}%")
        displayClientMessage("§7Status: §${if (xrayEnabled) "aEnabled" else "cDisabled"}")
    }

    /**
     * Reset XRay statistics
     */
    private fun resetStats() {
        blocksProcessed.set(0)
        blocksReplaced.set(0)
        chunksProcessed.set(0)
        loggedReplacements = 0
        displayClientMessage("§a[XRay] Statistics reset")
    }

    /**
     * Show block lists
     */
    private fun showBlockLists() {
        displayClientMessage("§6=== XRay Block Configuration ===")
        displayClientMessage("§aVisible blocks (${RuntimeBlockStates.getXRayVisibleBlocks().size}):")
        RuntimeBlockStates.getXRayVisibleBlocks().take(10).forEach { block ->
            displayClientMessage("§7  - $block")
        }
        if (RuntimeBlockStates.getXRayVisibleBlocks().size > 10) {
            displayClientMessage("§7  ... and ${RuntimeBlockStates.getXRayVisibleBlocks().size - 10} more")
        }
        
        displayClientMessage("§cHidden blocks (${RuntimeBlockStates.getXRayHiddenBlocks().size}):")
        RuntimeBlockStates.getXRayHiddenBlocks().take(10).forEach { block ->
            displayClientMessage("§7  - $block")
        }
        if (RuntimeBlockStates.getXRayHiddenBlocks().size > 10) {
            displayClientMessage("§7  ... and ${RuntimeBlockStates.getXRayHiddenBlocks().size - 10} more")
        }
    }

    /**
     * Show debug information
     */
    private fun showDebugInfo() {
        displayClientMessage("§6=== XRay Debug Information ===")
        displayClientMessage("§7XRay enabled: §f$xrayEnabled")
        val airDef = airBlockDefinition
        displayClientMessage("§7Air block definition: §f${if (airDef is org.cloudburstmc.protocol.common.NamedDefinition) airDef.identifier else "Not found"}")
        displayClientMessage("§7Air block runtime ID: §f${airBlockDefinition?.runtimeId ?: "N/A"}")
        displayClientMessage("§7Session duration: §f${formatDuration(System.currentTimeMillis() - startTime)}")
        displayClientMessage("§7Errors encountered: §f${errorsEncountered.get()}")
        displayClientMessage("§7Recent replacements tracked: §f${recentReplacements.size}")
        displayClientMessage("§7Block types replaced: §f${blockReplacementCounts.size}")

        displayClientMessage("§7Configuration:")
        displayClientMessage("§7  - Ores: §f$enableOres")
        displayClientMessage("§7  - Chests: §f$enableChests")
        displayClientMessage("§7  - Spawners: §f$enableSpawners")
        displayClientMessage("§7  - Fluids: §f$enableFluids")
        displayClientMessage("§7  - Bedrock: §f$enableBedrock")
        displayClientMessage("§7  - Detailed logging: §f$detailedLogging")
        displayClientMessage("§7  - Performance logging: §f$logPerformance")
    }

    /**
     * Show performance statistics
     */
    private fun showPerformanceStats() {
        displayClientMessage("§6=== XRay Performance Statistics ===")

        if (processingTimes.isNotEmpty()) {
            val avgTime = processingTimes.average()
            val maxTime = processingTimes.maxOrNull() ?: 0L
            val minTime = processingTimes.minOrNull() ?: 0L

            displayClientMessage("§7Processing times (ms):")
            displayClientMessage("§7  - Average: §f${"%.2f".format(avgTime)}")
            displayClientMessage("§7  - Maximum: §f$maxTime")
            displayClientMessage("§7  - Minimum: §f$minTime")
            displayClientMessage("§7  - Samples: §f${processingTimes.size}")
        } else {
            displayClientMessage("§7No performance data available")
        }

        val currentTime = System.currentTimeMillis()
        val sessionDuration = currentTime - startTime
        if (sessionDuration > 0) {
            val blocksPerSecond = (blocksProcessed.get() * 1000.0) / sessionDuration
            val chunksPerSecond = (chunksProcessed.get() * 1000.0) / sessionDuration

            displayClientMessage("§7Processing rates:")
            displayClientMessage("§7  - Blocks/sec: §f${"%.2f".format(blocksPerSecond)}")
            displayClientMessage("§7  - Chunks/sec: §f${"%.2f".format(chunksPerSecond)}")
        }
    }

    /**
     * Show recent block replacements
     */
    private fun showRecentReplacements() {
        displayClientMessage("§6=== Recent Block Replacements ===")

        if (recentReplacements.isEmpty()) {
            displayClientMessage("§7No recent replacements recorded")
            return
        }

        val formatter = DateTimeFormatter.ofPattern("HH:mm:ss")
        recentReplacements.takeLast(10).forEach { replacement ->
            displayClientMessage("§7${replacement.timestamp.format(formatter)} - §f${replacement.originalBlock} §7at §f${replacement.position}")
        }

        if (recentReplacements.size > 10) {
            displayClientMessage("§7... and ${recentReplacements.size - 10} more")
        }
    }

    /**
     * Show error log
     */
    private fun showErrorLog() {
        displayClientMessage("§6=== XRay Error Log ===")
        displayClientMessage("§7Total errors: §f${errorsEncountered.get()}")

        if (errorsEncountered.get() == 0L) {
            displayClientMessage("§a[XRay] No errors encountered!")
        } else {
            displayClientMessage("§c[XRay] Check console for detailed error messages")
        }
    }

    /**
     * Format duration in milliseconds to human readable format
     */
    private fun formatDuration(durationMs: Long): String {
        val seconds = durationMs / 1000
        val minutes = seconds / 60
        val hours = minutes / 60

        return when {
            hours > 0 -> "${hours}h ${minutes % 60}m ${seconds % 60}s"
            minutes > 0 -> "${minutes}m ${seconds % 60}s"
            else -> "${seconds}s"
        }
    }

    /**
     * Log block replacement with detailed information
     */
    private fun logBlockReplacement(originalBlock: String, position: String) {
        // Update replacement counts
        blockReplacementCounts.computeIfAbsent(originalBlock) { AtomicLong(0) }.incrementAndGet()

        // Add to recent replacements
        synchronized(recentReplacements) {
            recentReplacements.add(BlockReplacement(originalBlock, position, LocalDateTime.now()))
            if (recentReplacements.size > maxRecentReplacements) {
                recentReplacements.removeAt(0)
            }
        }

        // Log if enabled
        if (detailedLogging && loggedReplacements < maxLoggedReplacements) {
            displayClientMessage("§7[XRay] Replaced: $originalBlock -> air at $position")
            loggedReplacements++
        }
    }

    /**
     * Log performance data
     */
    private fun logPerformance(processingTime: Long) {
        if (logPerformance) {
            synchronized(processingTimes) {
                processingTimes.add(processingTime)
                if (processingTimes.size > 1000) {
                    processingTimes.removeAt(0)
                }
            }

            val currentTime = System.currentTimeMillis()
            if (currentTime - lastPerformanceLog > performanceLogInterval) {
                val avgTime = processingTimes.average()
                displayClientMessage("§7[XRay] Avg processing time: ${"%.2f".format(avgTime)}ms")
                lastPerformanceLog = currentTime
            }
        }
    }

    /**
     * Log error with increment
     */
    private fun logError(message: String, exception: Exception? = null) {
        errorsEncountered.incrementAndGet()
        displayClientMessage("§c[XRay] Error: $message")

        if (detailedLogging && exception != null) {
            displayClientMessage("§c[XRay] Exception: ${exception.message}")
            exception.printStackTrace()
        }
    }
}
