<variant
    name="debug"
    package="com.radiantbyte.aetherclient"
    minSdkVersion="28"
    targetSdkVersion="36.0"
    debuggable="true"
    mergedManifest="build\intermediates\merged_manifest\debug\processDebugMainManifest\AndroidManifest.xml"
    manifestMergeReport="build\outputs\logs\manifest-merger-debug-report.txt"
    proguardFiles="build\intermediates\default_proguard_files\global\proguard-android.txt-8.11.1"
    partialResultsDir="build\intermediates\lint_partial_results\debug\lintAnalyzeDebug\out">
  <buildFeatures
      namespacing="REQUIRED"/>
  <sourceProviders>
    <sourceProvider
        manifests="src\main\AndroidManifest.xml"
        javaDirectories="src\main\java;src\debug\java;src\main\kotlin;src\debug\kotlin"
        resDirectories="src\main\res;src\debug\res"
        assetsDirectories="src\main\assets;src\debug\assets"/>
  </sourceProviders>
  <testSourceProviders>
  </testSourceProviders>
  <testFixturesSourceProviders>
  </testFixturesSourceProviders>
  <artifact
      classOutputs="build\intermediates\javac\debug\compileDebugJavaWithJavac\classes;build\tmp\kotlin-classes\debug;build\intermediates\compile_r_class_jar\debug\generateDebugRFile\R.jar"
      type="MAIN"
      applicationId="com.radiantbyte.aetherclient"
      generatedSourceFolders="build\generated\ap_generated_sources\debug\out;build\generated\source\buildConfig\debug"
      desugaredMethodsFiles="C:\Users\<USER>\.gradle\caches\8.13\transforms\5aa2c574ea3f161eff9fa2f10c9fd80d\transformed\D8BackportedDesugaredMethods.txt">
  </artifact>
</variant>
