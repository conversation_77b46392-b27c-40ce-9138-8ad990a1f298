<!DOCTYPE html>
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
<meta http-equiv="x-ua-compatible" content="IE=edge"/>
<title>Test results - VersionMapperTest</title>
<link href="../css/base-style.css" rel="stylesheet" type="text/css"/>
<link href="../css/style.css" rel="stylesheet" type="text/css"/>
<script src="../js/report.js" type="text/javascript"></script>
</head>
<body>
<div id="content">
<h1>VersionMapperTest</h1>
<div class="breadcrumbs">
<a href="../index.html">all</a> &gt; 
<a href="../packages/com.radiantbyte.aetherproxy.version.html">com.radiantbyte.aetherproxy.version</a> &gt; VersionMapperTest</div>
<div id="summary">
<table>
<tr>
<td>
<div class="summaryGroup">
<table>
<tr>
<td>
<div class="infoBox" id="tests">
<div class="counter">8</div>
<p>tests</p>
</div>
</td>
<td>
<div class="infoBox" id="failures">
<div class="counter">4</div>
<p>failures</p>
</div>
</td>
<td>
<div class="infoBox" id="ignored">
<div class="counter">0</div>
<p>ignored</p>
</div>
</td>
<td>
<div class="infoBox" id="duration">
<div class="counter">0.710s</div>
<p>duration</p>
</div>
</td>
</tr>
</table>
</div>
</td>
<td>
<div class="infoBox failures" id="successRate">
<div class="percent">50%</div>
<p>successful</p>
</div>
</td>
</tr>
</table>
</div>
<div id="tabs">
<ul class="tabLinks">
<li>
<a href="#tab0">Failed tests</a>
</li>
<li>
<a href="#tab1">Tests</a>
</li>
<li>
<a href="#tab2">Standard output</a>
</li>
<li>
<a href="#tab3">Standard error</a>
</li>
</ul>
<div id="tab0" class="tab">
<h2>Failed tests</h2>
<div class="test">
<a name="testGetCodecForVersion()"></a>
<h3 class="failures">testGetCodecForVersion()</h3>
<span class="code">
<pre>org.opentest4j.AssertionFailedError: expected: &lt;818&gt; but was: &lt;819&gt;
	at app//org.junit.jupiter.api.AssertionFailureBuilder.build(AssertionFailureBuilder.java:151)
	at app//org.junit.jupiter.api.AssertionFailureBuilder.buildAndThrow(AssertionFailureBuilder.java:132)
	at app//org.junit.jupiter.api.AssertEquals.failNotEqual(AssertEquals.java:197)
	at app//org.junit.jupiter.api.AssertEquals.assertEquals(AssertEquals.java:150)
	at app//org.junit.jupiter.api.AssertEquals.assertEquals(AssertEquals.java:145)
	at app//org.junit.jupiter.api.Assertions.assertEquals(Assertions.java:531)
	at app//com.radiantbyte.aetherproxy.version.VersionMapperTest.testGetCodecForVersion(VersionMapperTest.kt:15)
	at java.base@21.0.6/java.lang.reflect.Method.invoke(Method.java:580)
	at java.base@21.0.6/java.util.ArrayList.forEach(ArrayList.java:1596)
	at java.base@21.0.6/java.util.ArrayList.forEach(ArrayList.java:1596)
</pre>
</span>
</div>
<div class="test">
<a name="testGetProtocolForVersion()"></a>
<h3 class="failures">testGetProtocolForVersion()</h3>
<span class="code">
<pre>org.opentest4j.AssertionFailedError: expected: &lt;818&gt; but was: &lt;819&gt;
	at app//org.junit.jupiter.api.AssertionFailureBuilder.build(AssertionFailureBuilder.java:151)
	at app//org.junit.jupiter.api.AssertionFailureBuilder.buildAndThrow(AssertionFailureBuilder.java:132)
	at app//org.junit.jupiter.api.AssertEquals.failNotEqual(AssertEquals.java:197)
	at app//org.junit.jupiter.api.AssertEquals.assertEquals(AssertEquals.java:150)
	at app//org.junit.jupiter.api.AssertEquals.assertEquals(AssertEquals.java:145)
	at app//org.junit.jupiter.api.Assertions.assertEquals(Assertions.java:531)
	at app//com.radiantbyte.aetherproxy.version.VersionMapperTest.testGetProtocolForVersion(VersionMapperTest.kt:34)
	at java.base@21.0.6/java.lang.reflect.Method.invoke(Method.java:580)
	at java.base@21.0.6/java.util.ArrayList.forEach(ArrayList.java:1596)
	at java.base@21.0.6/java.util.ArrayList.forEach(ArrayList.java:1596)
</pre>
</span>
</div>
<div class="test">
<a name="testGetSupportedVersions()"></a>
<h3 class="failures">testGetSupportedVersions()</h3>
<span class="code">
<pre>org.opentest4j.AssertionFailedError: expected: &lt;true&gt; but was: &lt;false&gt;
	at app//org.junit.jupiter.api.AssertionFailureBuilder.build(AssertionFailureBuilder.java:151)
	at app//org.junit.jupiter.api.AssertionFailureBuilder.buildAndThrow(AssertionFailureBuilder.java:132)
	at app//org.junit.jupiter.api.AssertTrue.failNotTrue(AssertTrue.java:63)
	at app//org.junit.jupiter.api.AssertTrue.assertTrue(AssertTrue.java:36)
	at app//org.junit.jupiter.api.AssertTrue.assertTrue(AssertTrue.java:31)
	at app//org.junit.jupiter.api.Assertions.assertTrue(Assertions.java:183)
	at app//com.radiantbyte.aetherproxy.version.VersionMapperTest.testGetSupportedVersions(VersionMapperTest.kt:68)
	at java.base@21.0.6/java.lang.reflect.Method.invoke(Method.java:580)
	at java.base@21.0.6/java.util.ArrayList.forEach(ArrayList.java:1596)
	at java.base@21.0.6/java.util.ArrayList.forEach(ArrayList.java:1596)
</pre>
</span>
</div>
<div class="test">
<a name="testIsVersionSupported()"></a>
<h3 class="failures">testIsVersionSupported()</h3>
<span class="code">
<pre>org.opentest4j.AssertionFailedError: expected: &lt;true&gt; but was: &lt;false&gt;
	at app//org.junit.jupiter.api.AssertionFailureBuilder.build(AssertionFailureBuilder.java:151)
	at app//org.junit.jupiter.api.AssertionFailureBuilder.buildAndThrow(AssertionFailureBuilder.java:132)
	at app//org.junit.jupiter.api.AssertTrue.failNotTrue(AssertTrue.java:63)
	at app//org.junit.jupiter.api.AssertTrue.assertTrue(AssertTrue.java:36)
	at app//org.junit.jupiter.api.AssertTrue.assertTrue(AssertTrue.java:31)
	at app//org.junit.jupiter.api.Assertions.assertTrue(Assertions.java:183)
	at app//com.radiantbyte.aetherproxy.version.VersionMapperTest.testIsVersionSupported(VersionMapperTest.kt:40)
	at java.base@21.0.6/java.lang.reflect.Method.invoke(Method.java:580)
	at java.base@21.0.6/java.util.ArrayList.forEach(ArrayList.java:1596)
	at java.base@21.0.6/java.util.ArrayList.forEach(ArrayList.java:1596)
</pre>
</span>
</div>
</div>
<div id="tab1" class="tab">
<h2>Tests</h2>
<table>
<thead>
<tr>
<th>Test</th>
<th>Duration</th>
<th>Result</th>
</tr>
</thead>
<tr>
<td class="success">testGetCodecForProtocol()</td>
<td class="success">0.001s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="failures">testGetCodecForVersion()</td>
<td class="failures">0.001s</td>
<td class="failures">failed</td>
</tr>
<tr>
<td class="failures">testGetProtocolForVersion()</td>
<td class="failures">0.001s</td>
<td class="failures">failed</td>
</tr>
<tr>
<td class="success">testGetSupportedProtocols()</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="failures">testGetSupportedVersions()</td>
<td class="failures">0.002s</td>
<td class="failures">failed</td>
</tr>
<tr>
<td class="success">testIsProtocolSupported()</td>
<td class="success">0.001s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="failures">testIsVersionSupported()</td>
<td class="failures">0.702s</td>
<td class="failures">failed</td>
</tr>
<tr>
<td class="success">testUnknownVersionFallback()</td>
<td class="success">0.002s</td>
<td class="success">passed</td>
</tr>
</table>
</div>
<div id="tab2" class="tab">
<h2>Standard output</h2>
<span class="code">
<pre>[00:35:59 DEBUG]: Using Log4J2 as the default logging framework
[00:35:59 DEBUG]: -Dio.netty.noUnsafe: false
[00:35:59 DEBUG]: Java version: 21
[00:35:59 DEBUG]: sun.misc.Unsafe.theUnsafe: available
[00:35:59 DEBUG]: sun.misc.Unsafe base methods: all available
[00:35:59 DEBUG]: java.nio.Buffer.address: available
[00:35:59 DEBUG]: direct buffer constructor: unavailable: Reflective setAccessible(true) disabled
[00:35:59 DEBUG]: java.nio.Bits.unaligned: available, true
[00:35:59 DEBUG]: jdk.internal.misc.Unsafe.allocateUninitializedArray(int): unavailable: symbolic reference class is not accessible: class jdk.internal.misc.Unsafe, from class io.netty.util.internal.PlatformDependent0 (unnamed module @4efc180e)
[00:35:59 DEBUG]: java.nio.DirectByteBuffer.&lt;init&gt;(long, {int,long}): unavailable
[00:35:59 DEBUG]: sun.misc.Unsafe: available
[00:35:59 DEBUG]: -Dio.netty.tmpdir: C:\Users\<USER>\AppData\Local\Temp (java.io.tmpdir)
[00:35:59 DEBUG]: -Dio.netty.bitMode: 64 (sun.arch.data.model)
[00:35:59 DEBUG]: Platform: Windows
[00:35:59 DEBUG]: -Dio.netty.maxDirectMemory: -1 bytes
[00:35:59 DEBUG]: java.nio.ByteBuffer.cleaner(): available
[00:35:59 DEBUG]: -Dio.netty.noPreferDirect: false
VersionMapper: Unknown Minecraft version 'unknown.version', using default codec
VersionMapper: Unknown Minecraft version 'unknown.version', using default protocol
VersionMapper: Unknown Minecraft version '1.21.93', using default protocol
VersionMapper: Unknown Minecraft version '1.21.94', using default protocol
VersionMapper: Unknown Minecraft version '1.21.90', using default protocol
VersionMapper: Unknown Minecraft version '1.21.93', using default codec
VersionMapper: Unknown Minecraft version '1.21.90', using default codec
</pre>
</span>
</div>
<div id="tab3" class="tab">
<h2>Standard error</h2>
<span class="code">
<pre>SLF4J(W): No SLF4J providers were found.
SLF4J(W): Defaulting to no-operation (NOP) logger implementation
SLF4J(W): See https://www.slf4j.org/codes.html#noProviders for further details.
</pre>
</span>
</div>
</div>
<div id="footer">
<p>
<div>
<label class="hidden" id="label-for-line-wrapping-toggle" for="line-wrapping-toggle">Wrap lines
<input id="line-wrapping-toggle" type="checkbox" autocomplete="off"/>
</label>
</div>Generated by 
<a href="http://www.gradle.org">Gradle 8.10</a> at Jul 29, 2025, 12:35:59 AM</p>
</div>
</div>
</body>
</html>
