package com.radiantbyte.aetherproxy.module.motion

import com.radiantbyte.aetherproxy.util.command
import com.radiantbyte.aetherproxy.util.mismatch
import com.radiantbyte.aetherproxy.event.handler.packet
import com.radiantbyte.aetherproxy.module.Module
import com.radiantbyte.aetherproxy.module.ModuleCategory
import com.radiantbyte.aetherproxy.session.AetherSession
import org.cloudburstmc.math.vector.Vector3f
import org.cloudburstmc.protocol.bedrock.data.Ability
import org.cloudburstmc.protocol.bedrock.data.AbilityLayer
import org.cloudburstmc.protocol.bedrock.data.PlayerAuthInputData
import org.cloudburstmc.protocol.bedrock.data.PlayerPermission
import org.cloudburstmc.protocol.bedrock.data.command.CommandPermission
import org.cloudburstmc.protocol.bedrock.packet.PlayerAuthInputPacket
import org.cloudburstmc.protocol.bedrock.packet.RequestAbilityPacket
import org.cloudburstmc.protocol.bedrock.packet.SetEntityMotionPacket
import org.cloudburstmc.protocol.bedrock.packet.UpdateAbilitiesPacket

class NoClipModule(aetherSession: AetherSession) : Module(aetherSession, "NoClip", ModuleCategory.Motion) {

    private val moveSpeed by floatValue("speed", 0.15f, 0.1f..1.5f)
    private val maintainGroundLevel by boolValue("maintain_ground_level", true)
    private val allowVerticalMovement by boolValue("allow_vertical_movement", true)

    private var noClipEnabled = false
    private var groundLevel: Float? = null
    private var isFlying = false
    private var lastVerticalInput = 0f
    private var lastMotionTime = 0L
    private val motionInterval = 50L // 50ms between motion updates

    private val enableNoClipAbilitiesPacket = UpdateAbilitiesPacket().apply {
        playerPermission = PlayerPermission.OPERATOR
        commandPermission = CommandPermission.OWNER
        abilityLayers.add(AbilityLayer().apply {
            layerType = AbilityLayer.Type.BASE
            abilitiesSet.addAll(Ability.entries.toTypedArray())
            abilityValues.addAll(
                arrayOf(
                    Ability.BUILD,
                    Ability.MINE,
                    Ability.DOORS_AND_SWITCHES,
                    Ability.OPEN_CONTAINERS,
                    Ability.ATTACK_PLAYERS,
                    Ability.ATTACK_MOBS,
                    Ability.MAY_FLY,
                    Ability.FLY_SPEED,
                    Ability.WALK_SPEED,
                    Ability.OPERATOR_COMMANDS
                )
            )
            abilityValues.add(Ability.NO_CLIP)
            walkSpeed = 0.1f
            flySpeed = 0.15f
        })
    }

    private val disableNoClipAbilitiesPacket = UpdateAbilitiesPacket().apply {
        playerPermission = PlayerPermission.OPERATOR
        commandPermission = CommandPermission.OWNER
        abilityLayers.add(AbilityLayer().apply {
            layerType = AbilityLayer.Type.BASE
            abilitiesSet.addAll(Ability.entries.toTypedArray())
            abilityValues.addAll(
                arrayOf(
                    Ability.BUILD,
                    Ability.MINE,
                    Ability.DOORS_AND_SWITCHES,
                    Ability.OPEN_CONTAINERS,
                    Ability.ATTACK_PLAYERS,
                    Ability.ATTACK_MOBS,
                    Ability.FLY_SPEED,
                    Ability.WALK_SPEED,
                    Ability.OPERATOR_COMMANDS
                )
            )
            abilityValues.remove(Ability.NO_CLIP)
            walkSpeed = 0.1f
        })
    }

    init {
        aetherSession.command(
            "noclip",
            "Toggle NoClip mode with improved ground level maintenance",
            handler = { arguments ->
                when (arguments.size) {
                    0 -> toggle()
                    else -> mismatch()
                }
            }
        )

        packet<RequestAbilityPacket> { packetEvent, _ ->
            if (isEnabled() && packetEvent.packet.ability == Ability.NO_CLIP) {
                packetEvent.consume()
            }
        }

        packet<UpdateAbilitiesPacket> { packetEvent, _ ->
            if (isEnabled()) {
                packetEvent.consume()
            }
        }

        packet<PlayerAuthInputPacket> { packetEvent, _ ->
            val packet = packetEvent.packet
            val currentTime = System.currentTimeMillis()

            // Enable/disable NoClip abilities
            if (!noClipEnabled && isEnabled()) {
                enableNoClipAbilitiesPacket.uniqueEntityId = aetherSession.localPlayer.uniqueEntityId
                aetherSession.inbound(enableNoClipAbilitiesPacket)
                noClipEnabled = true
                
                // Store ground level when first enabled
                if (maintainGroundLevel && groundLevel == null) {
                    groundLevel = aetherSession.localPlayer.position.y
                    displayClientMessage("§a[NoClip] Ground level set to: ${String.format("%.2f", groundLevel)}")
                }

                displayClientMessage("§a[NoClip] Enabled - Use space/shift for vertical movement")
                return@packet
            } else if (noClipEnabled && !isEnabled()) {
                disableNoClipAbilitiesPacket.uniqueEntityId = aetherSession.localPlayer.uniqueEntityId
                aetherSession.inbound(disableNoClipAbilitiesPacket)
                noClipEnabled = false
                groundLevel = null
                isFlying = false
                displayClientMessage("§c[NoClip] Module disabled")
                return@packet
            }

            if (!isEnabled() || currentTime - lastMotionTime < motionInterval) {
                return@packet
            }

            if (isEnabled()) {
                var verticalMotion = 0f
                val hasVerticalInput = packet.inputData.contains(PlayerAuthInputData.JUMPING) || 
                                     packet.inputData.contains(PlayerAuthInputData.SNEAKING)

                if (allowVerticalMovement) {
                    // Handle vertical movement
                    if (packet.inputData.contains(PlayerAuthInputData.JUMPING)) {
                        verticalMotion = moveSpeed
                        isFlying = true
                        lastVerticalInput = moveSpeed
                    } else if (packet.inputData.contains(PlayerAuthInputData.SNEAKING)) {
                        verticalMotion = -moveSpeed
                        isFlying = true
                        lastVerticalInput = -moveSpeed
                    } else {
                        // No vertical input
                        if (isFlying && maintainGroundLevel && groundLevel != null) {
                            // If we were flying and now have no input, maintain current Y or return to ground
                            val currentY = aetherSession.localPlayer.position.y
                            if (currentY > groundLevel!! + 0.5f) {
                                // If significantly above ground, allow natural falling
                                isFlying = false
                                verticalMotion = 0f // Let gravity take over
                            } else {
                                // Stay at ground level
                                verticalMotion = 0f
                                isFlying = false
                            }
                        } else {
                            isFlying = false
                            verticalMotion = 0f
                        }
                        lastVerticalInput = 0f
                    }
                }

                // Apply motion if needed
                if (verticalMotion != 0f || (maintainGroundLevel && groundLevel != null && !hasVerticalInput && !isFlying)) {
                    val targetY = if (maintainGroundLevel && groundLevel != null && !hasVerticalInput && !isFlying) {
                        groundLevel!!
                    } else {
                        aetherSession.localPlayer.position.y + verticalMotion
                    }

                    val motionPacket = SetEntityMotionPacket().apply {
                        runtimeEntityId = aetherSession.localPlayer.runtimeEntityId
                        motion = Vector3f.from(
                            aetherSession.localPlayer.motion.x,
                            if (maintainGroundLevel && groundLevel != null && !hasVerticalInput && !isFlying) {
                                0f // No vertical motion when maintaining ground level
                            } else {
                                verticalMotion
                            },
                            aetherSession.localPlayer.motion.z
                        )
                    }
                    aetherSession.inbound(motionPacket)
                    lastMotionTime = currentTime
                }
            }
        }
    }


}
