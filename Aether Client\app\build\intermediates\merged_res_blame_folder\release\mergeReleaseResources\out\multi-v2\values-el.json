{"logs": [{"outputFile": "com.radiantbyte.aetherclient.app-mergeReleaseResources-46:/values-el/values-el.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\76f6aeeb7c72cabab8c29d18f4d3161d\\transformed\\core-1.16.0\\res\\values-el\\values-el.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,256,356,459,567,673,790", "endColumns": "97,102,99,102,107,105,116,100", "endOffsets": "148,251,351,454,562,668,785,886"}, "to": {"startLines": "3,4,5,6,7,8,9,83", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "191,289,392,492,595,703,809,8785", "endColumns": "97,102,99,102,107,105,116,100", "endOffsets": "284,387,487,590,698,804,921,8881"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\d61819a9c261b5407a6ee59c046430b9\\transformed\\ui-release\\res\\values-el\\values-el.xml", "from": {"startLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "192,291,377,478,583,675,756,850,939,1029,1110,1192,1267,1356,1431,1509,1584,1663,1733", "endColumns": "98,85,100,104,91,80,93,88,89,80,81,74,88,74,77,74,78,69,122", "endOffsets": "286,372,473,578,670,751,845,934,1024,1105,1187,1262,1351,1426,1504,1579,1658,1728,1851"}, "to": {"startLines": "10,11,12,13,14,15,16,74,75,76,77,78,79,80,81,82,84,85,86", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "926,1025,1111,1212,1317,1409,1490,8051,8140,8230,8311,8393,8468,8557,8632,8710,8886,8965,9035", "endColumns": "98,85,100,104,91,80,93,88,89,80,81,74,88,74,77,74,78,69,122", "endOffsets": "1020,1106,1207,1312,1404,1485,1579,8135,8225,8306,8388,8463,8552,8627,8705,8780,8960,9030,9153"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\7abcdc53ecef1f011b8484f7921702b8\\transformed\\foundation-release\\res\\values-el\\values-el.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,141,239", "endColumns": "85,97,101", "endOffsets": "136,234,336"}, "to": {"startLines": "2,87,88", "startColumns": "4,4,4", "startOffsets": "105,9158,9256", "endColumns": "85,97,101", "endOffsets": "186,9251,9353"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\13ceaa855c6ae6b23241ae42f3bae2bf\\transformed\\material3-release\\res\\values-el\\values-el.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,179,303,417,539,643,746,866,1017,1145,1303,1393,1493,1592,1697,1815,1941,2046,2188,2324,2468,2648,2786,2906,3033,3157,3257,3356,3492,3629,3735,3841,3951,4095,4248,4362,4468,4555,4653,4750,4863,4953,5042,5145,5225,5308,5407,5509,5606,5704,5791,5897,5996,6098,6219,6299,6415", "endColumns": "123,123,113,121,103,102,119,150,127,157,89,99,98,104,117,125,104,141,135,143,179,137,119,126,123,99,98,135,136,105,105,109,143,152,113,105,86,97,96,112,89,88,102,79,82,98,101,96,97,86,105,98,101,120,79,115,106", "endOffsets": "174,298,412,534,638,741,861,1012,1140,1298,1388,1488,1587,1692,1810,1936,2041,2183,2319,2463,2643,2781,2901,3028,3152,3252,3351,3487,3624,3730,3836,3946,4090,4243,4357,4463,4550,4648,4745,4858,4948,5037,5140,5220,5303,5402,5504,5601,5699,5786,5892,5991,6093,6214,6294,6410,6517"}, "to": {"startLines": "17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1584,1708,1832,1946,2068,2172,2275,2395,2546,2674,2832,2922,3022,3121,3226,3344,3470,3575,3717,3853,3997,4177,4315,4435,4562,4686,4786,4885,5021,5158,5264,5370,5480,5624,5777,5891,5997,6084,6182,6279,6392,6482,6571,6674,6754,6837,6936,7038,7135,7233,7320,7426,7525,7627,7748,7828,7944", "endColumns": "123,123,113,121,103,102,119,150,127,157,89,99,98,104,117,125,104,141,135,143,179,137,119,126,123,99,98,135,136,105,105,109,143,152,113,105,86,97,96,112,89,88,102,79,82,98,101,96,97,86,105,98,101,120,79,115,106", "endOffsets": "1703,1827,1941,2063,2167,2270,2390,2541,2669,2827,2917,3017,3116,3221,3339,3465,3570,3712,3848,3992,4172,4310,4430,4557,4681,4781,4880,5016,5153,5259,5365,5475,5619,5772,5886,5992,6079,6177,6274,6387,6477,6566,6669,6749,6832,6931,7033,7130,7228,7315,7421,7520,7622,7743,7823,7939,8046"}}]}]}