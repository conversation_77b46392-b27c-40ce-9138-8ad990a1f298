package com.radiantbyte.aetherproxy.util

import org.cloudburstmc.nbt.NbtMap
import org.cloudburstmc.nbt.NbtUtils
import org.cloudburstmc.protocol.bedrock.data.definitions.BlockDefinition
import java.io.ByteArrayOutputStream
import java.util.*
import java.util.concurrent.ConcurrentHashMap


object BlockPaletteUtils {

    fun createHash(block: NbtMap): Int {
        if (block.getString("name") == "minecraft:unknown") {
            return -2 // This is special case
        }
        // Order required
        val states = TreeMap(block.getCompound("states"))
        val statesBuilder = NbtMap.builder()
        statesBuilder.putAll(states)

        val tag = NbtMap.builder()
            .putString("name", block.getString("name"))
            .putCompound("states", statesBuilder.build())
            .build()

        val bytes: ByteArray?
        ByteArrayOutputStream().use { stream ->
            NbtUtils.createWriterLE(stream).use { outputStream ->
                outputStream.writeTag(tag)
                bytes = stream.toByteArray()
            }
        }

        return fnv1a_32(bytes!!)
    }

    private const val FNV1_32_INIT = -0x7ee3623b
    private const val FNV1_PRIME_32 = 0x01000193

    private fun fnv1a_32(data: ByteArray): Int {
        var hash = FNV1_32_INIT
        for (datum in data) {
            hash = hash xor (datum.toInt() and 0xff)
            hash *= FNV1_PRIME_32
        }
        return hash
    }

}

/**
 * Runtime block states manager for dynamic block manipulation
 * Used primarily for XRay functionality to track and modify block visibility
 */
object RuntimeBlockStates {

    // Cache for block name to runtime ID mapping
    private val blockNameToRuntimeId = ConcurrentHashMap<String, Int>()

    // Cache for runtime ID to block name mapping
    private val runtimeIdToBlockName = ConcurrentHashMap<Int, String>()

    // Air block runtime ID cache
    private var airBlockRuntimeId: Int? = null

    // XRay visible blocks (blocks that should remain visible in XRay mode)
    private val xrayVisibleBlocks = mutableSetOf<String>()

    // XRay hidden blocks (blocks that should be hidden/replaced with air)
    private val xrayHiddenBlocks = mutableSetOf<String>()

    init {
        // Initialize default XRay visible blocks (valuable/important blocks)
        xrayVisibleBlocks.addAll(listOf(
            "minecraft:diamond_ore",
            "minecraft:deepslate_diamond_ore",
            "minecraft:gold_ore",
            "minecraft:deepslate_gold_ore",
            "minecraft:iron_ore",
            "minecraft:deepslate_iron_ore",
            "minecraft:coal_ore",
            "minecraft:deepslate_coal_ore",
            "minecraft:copper_ore",
            "minecraft:deepslate_copper_ore",
            "minecraft:lapis_ore",
            "minecraft:deepslate_lapis_ore",
            "minecraft:redstone_ore",
            "minecraft:deepslate_redstone_ore",
            "minecraft:emerald_ore",
            "minecraft:deepslate_emerald_ore",
            "minecraft:ancient_debris",
            "minecraft:nether_gold_ore",
            "minecraft:nether_quartz_ore",
            "minecraft:chest",
            "minecraft:trapped_chest",
            "minecraft:ender_chest",
            "minecraft:shulker_box",
            "minecraft:spawner",
            "minecraft:end_portal_frame",
            "minecraft:bedrock",
            "minecraft:barrier",
            "minecraft:air",
            "minecraft:cave_air",
            "minecraft:void_air",
            "minecraft:water",
            "minecraft:flowing_water",
            "minecraft:lava",
            "minecraft:flowing_lava"
        ))

        // Initialize default XRay hidden blocks (common blocks that obstruct view)
        xrayHiddenBlocks.addAll(listOf(
            "minecraft:stone",
            "minecraft:granite",
            "minecraft:diorite",
            "minecraft:andesite",
            "minecraft:deepslate",
            "minecraft:cobblestone",
            "minecraft:cobbled_deepslate",
            "minecraft:dirt",
            "minecraft:grass_block",
            "minecraft:sand",
            "minecraft:sandstone",
            "minecraft:red_sand",
            "minecraft:red_sandstone",
            "minecraft:gravel",
            "minecraft:netherrack",
            "minecraft:end_stone",
            "minecraft:tuff",
            "minecraft:calcite",
            "minecraft:dripstone_block"
        ))
    }

    /**
     * Initialize the runtime block states with block definitions
     */
    fun initialize(blockDefinitions: Collection<BlockDefinition>) {
        blockNameToRuntimeId.clear()
        runtimeIdToBlockName.clear()

        for (definition in blockDefinitions) {
            // Get identifier from NamedDefinition interface if available
            val identifier = if (definition is org.cloudburstmc.protocol.common.NamedDefinition) {
                definition.identifier
            } else {
                "unknown_block_${definition.runtimeId}"
            }
            val runtimeId = definition.runtimeId

            blockNameToRuntimeId[identifier] = runtimeId
            runtimeIdToBlockName[runtimeId] = identifier

            // Cache air block runtime ID
            if (identifier == "minecraft:air") {
                airBlockRuntimeId = runtimeId
            }
        }

        println("RuntimeBlockStates: Initialized with ${blockDefinitions.size} block definitions")
        println("RuntimeBlockStates: Air block runtime ID: $airBlockRuntimeId")
    }

    /**
     * Get runtime ID for a block name
     */
    fun getRuntimeId(blockName: String): Int? {
        return blockNameToRuntimeId[blockName]
    }

    /**
     * Get block name for a runtime ID
     */
    fun getBlockName(runtimeId: Int): String? {
        return runtimeIdToBlockName[runtimeId]
    }

    /**
     * Get the air block runtime ID
     */
    fun getAirBlockRuntimeId(): Int? {
        return airBlockRuntimeId
    }

    /**
     * Check if a block should be visible in XRay mode
     */
    fun isXRayVisible(blockName: String): Boolean {
        return xrayVisibleBlocks.contains(blockName)
    }

    /**
     * Check if a block should be visible in XRay mode by runtime ID
     */
    fun isXRayVisible(runtimeId: Int): Boolean {
        val blockName = getBlockName(runtimeId) ?: return false
        return isXRayVisible(blockName)
    }

    /**
     * Check if a block should be hidden in XRay mode
     */
    fun isXRayHidden(blockName: String): Boolean {
        return xrayHiddenBlocks.contains(blockName)
    }

    /**
     * Check if a block should be hidden in XRay mode by runtime ID
     */
    fun isXRayHidden(runtimeId: Int): Boolean {
        val blockName = getBlockName(runtimeId) ?: return false
        return isXRayHidden(blockName)
    }

    /**
     * Add a block to the XRay visible list
     */
    fun addXRayVisible(blockName: String) {
        xrayVisibleBlocks.add(blockName)
        xrayHiddenBlocks.remove(blockName) // Remove from hidden if present
    }

    /**
     * Add a block to the XRay hidden list
     */
    fun addXRayHidden(blockName: String) {
        xrayHiddenBlocks.add(blockName)
        xrayVisibleBlocks.remove(blockName) // Remove from visible if present
    }

    /**
     * Remove a block from XRay lists
     */
    fun removeFromXRayLists(blockName: String) {
        xrayVisibleBlocks.remove(blockName)
        xrayHiddenBlocks.remove(blockName)
    }

    /**
     * Get all XRay visible blocks
     */
    fun getXRayVisibleBlocks(): Set<String> {
        return xrayVisibleBlocks.toSet()
    }

    /**
     * Get all XRay hidden blocks
     */
    fun getXRayHiddenBlocks(): Set<String> {
        return xrayHiddenBlocks.toSet()
    }

    /**
     * Clear all runtime data
     */
    fun clear() {
        blockNameToRuntimeId.clear()
        runtimeIdToBlockName.clear()
        airBlockRuntimeId = null
    }
}
